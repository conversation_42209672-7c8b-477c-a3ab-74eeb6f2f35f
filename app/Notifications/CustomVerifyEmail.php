<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Notifications\Messages\MailMessage;
use App\Models\User;

class CustomVerifyEmail extends VerifyEmail implements ShouldQueue
{
    use Queueable;

    /**
     * The user instance.
     *
     * @var User
     */
    public $user;

    /**
     * Create a new notification instance.
     *
     * @param  User  $user
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;
        \Log::info('CustomVerifyEmail notification created', [
            'user_id' => $user->id,
            'email' => $user->email,
            'queue' => $this->queue
        ]);
    }

    /**
     * Get the channels the notification should be sent through.
     *
     * @param  mixed  $notifiable
     * @return array|string
     */
    public function via($notifiable)
    {
        \Log::info('CustomVerifyEmail via method called', [
            'user_id' => $notifiable->id,
            'email' => $notifiable->email
        ]);
        return ['mail'];
    }

    /**
     * Get the verification URL for the given notifiable.
     *
     * @param  mixed  $notifiable
     * @return string
     */
    protected function verificationUrl($notifiable)
    {
        return parent::verificationUrl($notifiable);
    }

    /**
     * Get the verify email notification mail message for the given URL.
     *
     * @param  string  $url
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    protected function buildMailMessage($url)
    {
        // Generate verification code
        $verificationCode = $this->user->generateVerificationCode();
        
        // Log the email sending attempt
        \Log::info('Sending verification email', [
            'user_id' => $this->user->id,
            'email' => $this->user->email,
            'verification_code' => $verificationCode,
            'url' => $url
        ]);
        
        try {
            return (new MailMessage)
                ->subject('Verify Email Address')
                ->markdown('emails.verify-email', [
                    'user' => $this->user,
                    'verificationCode' => $verificationCode,
                    'verificationUrl' => $url
                ]);
        } catch (\Exception $e) {
            \Log::error('Failed to send verification email', [
                'user_id' => $this->user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
