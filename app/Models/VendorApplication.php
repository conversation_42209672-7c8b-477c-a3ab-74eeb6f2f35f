<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class VendorApplication extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'business_type',
        'business_name',
        'business_email',
        'phone_number',
        'business_address',
        'business_description',
        'website',
        'tax_id',
        'id_type',
        'id_number',
        'id_document_path',
        'business_document_path',
        'status',
        'rejection_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'string',
    ];

    /**
     * Get the user that owns the application.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the URL for the ID document.
     */
    public function getIdDocumentUrlAttribute(): ?string
    {
        return $this->id_document_path ? Storage::url($this->id_document_path) : null;
    }

    /**
     * Get the URL for the business document.
     */
    public function getBusinessDocumentUrlAttribute(): ?string
    {
        return $this->business_document_path ? Storage::url($this->business_document_path) : null;
    }

    /**
     * Scope a query to only include pending applications.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include approved applications.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include rejected applications.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }
}
