<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Notifications\CustomVerifyEmail;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'verification_code',
        'verification_code_expires_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    /**
     * Send the email verification notification.
     *
     * @return void
     */
    /**
     * Send the email verification notification.
     *
     * @return void
     */
    public function sendEmailVerificationNotification()
    {
        $this->notify(new \App\Notifications\CustomVerifyEmail($this));
    }

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'verification_code',
        'verification_code_expires_at',
    ];

    /**
     * Generate a new verification code
     *
     * @return string
     */
    public function generateVerificationCode()
    {
        $this->verification_code = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
        $this->verification_code_expires_at = now()->addHours(24); // Code expires in 24 hours
        $this->save();
        
        return $this->verification_code;
    }
    
    /**
     * Check if verification code is valid
     *
     * @param string $code
     * @return bool
     */
    public function isValidVerificationCode($code)
    {
        if ($this->verification_code !== $code) {
            return false;
        }
        
        if (!$this->verification_code_expires_at) {
            return false;
        }
        
        // Ensure we have a Carbon instance
        $expiresAt = \Carbon\Carbon::parse($this->verification_code_expires_at);
        
        return $expiresAt->isFuture();
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'verification_code_expires_at' => 'datetime',
        ];
    }
}
