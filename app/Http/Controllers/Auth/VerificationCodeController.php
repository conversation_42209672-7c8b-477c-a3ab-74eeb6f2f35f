<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class VerificationCodeController extends Controller
{
    /**
     * Show the verification code form
     *
     * @return \Inertia\Response
     */
    public function show()
    {
        if (Auth::user()->hasVerifiedEmail()) {
            return redirect()->intended(route('dashboard'));
        }

        return Inertia::render('auth/verify-code');
    }

    /**
     * Verify the code
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function verify(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
        ]);

        $user = Auth::user();

        if ($user->isValidVerificationCode($request->code)) {
            $user->markEmailAsVerified();
            $user->verification_code = null;
            $user->verification_code_expires_at = null;
            $user->save();

            return redirect()->intended(route('dashboard'));
        }

        return back()->withErrors([
            'code' => 'The provided code is invalid or has expired.',
        ]);
    }

    /**
     * Resend the verification code
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resend()
    {
        $user = Auth::user();
        
        if ($user->hasVerifiedEmail()) {
            return redirect()->intended(route('dashboard'));
        }

        $user->sendEmailVerificationNotification();

        return back()->with('status', 'A new verification code has been sent to your email.');
    }
}
