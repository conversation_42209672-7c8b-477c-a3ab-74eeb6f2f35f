<?php

namespace App\Http\Controllers;

use App\Services\MonnifyService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PaymentController extends Controller
{
    protected $monnify;

    public function __construct(MonnifyService $monnify)
    {
        $this->monnify = $monnify;
    }

    public function initialize(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:100',
        ]);

        $reference = 'WALLET-' . Str::upper(Str::random(10));
        
        try {
            $response = $this->monnify->initializeTransaction([
                'amount' => $request->amount,
                'customer_name' => $request->user()->name,
                'customer_email' => $request->user()->email,
                'customer_phone' => $request->user()->phone ?? '08000000000',
                'reference' => $reference,
                'description' => 'Wallet Funding',
            ]);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'checkout_url' => $response['checkoutUrl'],
                    'reference' => $reference,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to initialize payment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function callback(Request $request)
    {
        $transactionReference = $request->query('paymentReference');
        
        if (!$transactionReference) {
            return redirect()->route('wallet.fund')
                ->with('error', 'Invalid payment reference');
        }

        try {
            $transaction = $this->monnify->verifyTransaction($transactionReference);
            
            if ($transaction['paymentStatus'] === 'PAID') {
                // TODO: Credit user's wallet here
                return redirect()->route('wallet.fund')
                    ->with('success', 'Wallet funded successfully!');
            }

            return redirect()->route('wallet.fund')
                ->with('error', 'Payment was not successful');

        } catch (\Exception $e) {
            return redirect()->route('wallet.fund')
                ->with('error', 'Failed to verify payment: ' . $e->getMessage());
        }
    }
}
