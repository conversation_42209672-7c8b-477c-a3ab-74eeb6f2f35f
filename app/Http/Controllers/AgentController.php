<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use App\Models\AgentApplication;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class AgentController extends Controller
{
    /**
     * Show the agent application form
     */
    public function showApplicationForm()
    {
        return Inertia::render('Agents/ApplyAsAgent');
    }

    /**
     * Process agent application
     */
    public function apply(Request $request)
    {
        $validated = $request->validate([
            'business_name' => 'required|string|max:255',
            'business_address' => 'required|string|max:1000',
            'phone_number' => 'required|string|max:20',
            'id_type' => 'required|in:nin,voter,driver,passport',
            'id_number' => 'required|string|max:100',
            'id_document' => 'required|file|mimes:jpeg,png,jpg,pdf|max:5120', // 5MB
            'business_document' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'experience' => 'nullable|string|max:2000',
            'terms_accepted' => 'required|accepted',
        ]);

        // Handle file uploads
        $idDocumentPath = $this->uploadFile($request->file('id_document'), 'agent_documents/ids');
        $businessDocumentPath = $request->hasFile('business_document') 
            ? $this->uploadFile($request->file('business_document'), 'agent_documents/business')
            : null;

        // Create application
        $application = new AgentApplication([
            'user_id' => Auth::id(),
            'business_name' => $validated['business_name'],
            'business_address' => $validated['business_address'],
            'phone_number' => $validated['phone_number'],
            'id_type' => $validated['id_type'],
            'id_number' => $validated['id_number'],
            'id_document_path' => $idDocumentPath,
            'business_document_path' => $businessDocumentPath,
            'experience' => $validated['experience'] ?? null,
            'status' => 'pending',
        ]);

        $application->save();

        return response()->json([
            'message' => 'Application submitted successfully!',
            'application' => $application
        ], 201);
    }

    /**
     * Upload a file to storage
     */
    private function uploadFile($file, $directory)
    {
        $extension = $file->getClientOriginalExtension();
        $fileName = Str::random(40) . '.' . $extension;
        $path = $file->storeAs($directory, $fileName, 'public');
        return $path;
    }
}
