<?php

namespace App\Http\Controllers;

use App\Models\VendorApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class VendorController extends Controller
{
    /**
     * Show the vendor application form.
     */
    public function apply()
    {
        return Inertia::render('Vendors/ApplyAsVendor');
    }

    /**
     * Process the vendor application.
     */
    public function storeApplication(Request $request)
    {
        $validated = $request->validate([
            'business_type' => 'required|in:individual,company',
            'business_name' => 'required|string|max:255',
            'business_email' => 'required|email|max:255',
            'phone_number' => 'required|string|max:20',
            'business_address' => 'required|string',
            'business_description' => 'required|string',
            'website' => 'nullable|url',
            'tax_id' => 'nullable|string|max:100',
            'id_type' => 'required|string|in:nin,voter,driver,passport',
            'id_number' => 'required|string|max:100',
            'id_document' => 'required|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'business_document' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'terms' => 'required|accepted',
        ]);

        try {
            // Upload ID document
            $idDocumentPath = $request->file('id_document')->store('vendor/documents', 'public');
            
            // Upload business document if provided
            $businessDocumentPath = null;
            if ($request->hasFile('business_document')) {
                $businessDocumentPath = $request->file('business_document')->store('vendor/documents', 'public');
            }

            // Create vendor application
            $application = VendorApplication::create([
                'user_id' => Auth::id(),
                'business_type' => $validated['business_type'],
                'business_name' => $validated['business_name'],
                'business_email' => $validated['business_email'],
                'phone_number' => $validated['phone_number'],
                'business_address' => $validated['business_address'],
                'business_description' => $validated['business_description'],
                'website' => $validated['website'] ?? null,
                'tax_id' => $validated['tax_id'] ?? null,
                'id_type' => $validated['id_type'],
                'id_number' => $validated['id_number'],
                'id_document_path' => $idDocumentPath,
                'business_document_path' => $businessDocumentPath,
                'status' => 'pending',
            ]);

            return redirect()->route('dashboard')->with('success', 'Your vendor application has been submitted successfully! We will review your application and get back to you soon.');
        } catch (\Exception $e) {
            // Clean up uploaded files if there was an error
            if (isset($idDocumentPath) && Storage::disk('public')->exists($idDocumentPath)) {
                Storage::disk('public')->delete($idDocumentPath);
            }
            if (isset($businessDocumentPath) && $businessDocumentPath && Storage::disk('public')->exists($businessDocumentPath)) {
                Storage::disk('public')->delete($businessDocumentPath);
            }
            
            return back()->with('error', 'There was an error processing your application. Please try again.')->withInput();
        }
    }
}
