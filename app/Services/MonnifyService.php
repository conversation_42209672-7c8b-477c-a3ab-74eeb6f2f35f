<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MonnifyService
{
    protected $baseUrl;
    protected $apiKey;
    protected $secretKey;
    protected $contractCode;
    protected $accessToken;

    public function __construct()
    {
        $this->baseUrl = config('services.monnify.base_url', 'https://api.monnify.com/api/v1');
        $this->apiKey = config('services.monnify.api_key');
        $this->secretKey = config('services.monnify.secret_key');
        $this->contractCode = config('services.monnify.contract_code');
        $this->accessToken = $this->getAccessToken();
    }

    protected function getAccessToken()
    {
        $authString = base64_encode($this->apiKey . ':' . $this->secretKey);
        
        $response = Http::withHeaders([
            'Authorization' => 'Basic ' . $authString,
        ])->post("$this->baseUrl/auth/login");

        if ($response->successful()) {
            $data = $response->json();
            return $data['responseBody']['accessToken'];
        }

        Log::error('Failed to get Monnify access token', [
            'status' => $response->status(),
            'response' => $response->json()
        ]);
        
        throw new \Exception('Failed to authenticate with Monnify');
    }

    public function initializeTransaction(array $data)
    {
        $payload = [
            'amount' => $data['amount'],
            'customerName' => $data['customer_name'],
            'customerEmail' => $data['customer_email'],
            'customerPhoneNumber' => $data['customer_phone'],
            'paymentReference' => $data['reference'],
            'paymentDescription' => $data['description'] ?? 'Wallet Funding',
            'currencyCode' => 'NGN',
            'contractCode' => $this->contractCode,
            'redirectUrl' => route('wallet.fund.callback'),
            'paymentMethods' => ['CARD', 'ACCOUNT_TRANSFER'],
        ];

        $response = Http::withToken($this->accessToken)
            ->post("$this->baseUrl/merchant/transactions/init-transaction", $payload);

        if ($response->successful()) {
            return $response->json()['responseBody'];
        }

        Log::error('Failed to initialize Monnify transaction', [
            'status' => $response->status(),
            'response' => $response->json()
        ]);
        
        throw new \Exception('Failed to initialize payment');
    }

    public function verifyTransaction($transactionReference)
    {
        $response = Http::withBasicAuth($this->apiKey, $this->secretKey)
            ->get("$this->baseUrl/merchant/transactions/query", [
                'paymentReference' => $transactionReference
            ]);

        if ($response->successful()) {
            return $response->json()['responseBody'];
        }

        Log::error('Failed to verify Monnify transaction', [
            'transaction_reference' => $transactionReference,
            'status' => $response->status(),
            'response' => $response->json()
        ]);
        
        throw new \Exception('Failed to verify transaction');
    }
}
