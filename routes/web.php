<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\PaymentController;

// Include test routes
require __DIR__.'/mail-test.php';

Route::get('/', function () {
    return Inertia::render('welcome', [
        'appearance' => 'light'
    ]);
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
    
    // Agent Application
    Route::prefix('agent')->group(function () {
        Route::get('apply', [\App\Http\Controllers\AgentController::class, 'showApplicationForm'])
            ->name('agent.apply.page');
            
        Route::post('apply', [\App\Http\Controllers\AgentController::class, 'apply'])
            ->name('agent.apply.submit');
    });
    
    // Vendor Application
    Route::prefix('vendor')->group(function () {
        Route::get('apply', [\App\Http\Controllers\VendorController::class, 'apply'])
            ->name('vendor.apply.page');
            
        Route::post('apply', [\App\Http\Controllers\VendorController::class, 'storeApplication'])
            ->name('vendor.apply.submit');
    });

    Route::prefix('wallet')->group(function () {
        Route::get('fund', function () {
            return Inertia::render('wallet/FundWallet');
        })->name('wallet.fund');
        
        Route::post('pay/initialize', [PaymentController::class, 'initialize'])->name('payment.initialize');
        Route::get('pay/callback', [PaymentController::class, 'callback'])->name('wallet.fund.callback');
    });
});

// More protected routes that require email verification
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/airtime', function () {
        return Inertia::render('Airtime/AirtimePurchase');
    })->name('airtime');
    
    Route::get('/data', function () {
        return Inertia::render('Data/DataPurchase');
    })->name('data');
    
    Route::get('/tv', function () {
        return Inertia::render('TV/TVSubscription');
    })->name('tv');
    
    Route::get('/bills/electricity', function () {
        return Inertia::render('Bills/ElectricityBills');
    })->name('bills.electricity');
    
    Route::get('/exam-pins', function () {
        return Inertia::render('ExamPins/ExamPinPurchase');
    })->name('exam.pins');
    
    Route::get('/recharge-pin', function () {
        return Inertia::render('Recharge/RechargePinPurchase');
    })->name('recharge.pin');
    
    Route::get('/history', function () {
        return Inertia::render('History/TransactionHistory');
    })->name('history');
});

// Profile Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/profile', [\App\Http\Controllers\ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [\App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [\App\Http\Controllers\ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
require __DIR__.'/mail-test.php';
require __DIR__.'/mail-test-route.php';
