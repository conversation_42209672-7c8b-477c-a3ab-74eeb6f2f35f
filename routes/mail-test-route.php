<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;

Route::get('/test-mail', function () {
    $user = Auth::user();
    
    if (!$user) {
        return 'No authenticated user';
    }
    
    try {
        Mail::raw('This is a test email', function($message) use ($user) {
            $message->to($user->email)
                    ->subject('Test Email from Laravel');
        });
        
        return 'Test email sent to ' . $user->email;
    } catch (\Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
})->middleware('auth');
