<?php

$source = __DIR__ . '/public/images/logo.png';
$destination = __DIR__ . '/public/favicon.ico';

// Check if source image exists
if (!file_exists($source)) {
    die("Error: Source image not found at $source");
}

// Create a new image from source
$image = imagecreatefrompng($source);
if (!$image) {
    die("Error: Could not load source image");
}

// Create a blank image for the favicon
$favicon = imagecreatetruecolor(32, 32);
imagealphablending($favicon, false);
imagesavealpha($favicon, true);

// Resize the source image to 32x32
imagecopyresampled($favicon, $image, 0, 0, 0, 0, 32, 32, imagesx($image), imagesy($image));

// Save as favicon.ico
if (imagepng($favicon, $destination)) {
    echo "Favicon generated successfully at: $destination\n";
} else {
    echo "Error: Failed to generate favicon\n";
}

// Free up memory
imagedestroy($image);
imagedestroy($favicon);
