#!/bin/bash
set -e

# Exit if any command fails
echo "🚀 Starting deployment..."

# Install PHP dependencies
echo "📦 Installing PHP dependencies..."
composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev

# Install NPM dependencies
echo "📦 Installing Node.js dependencies..."
npm ci

# Build assets
echo "🔨 Building assets..."
npm run build

# Clear caches
echo "🧹 Clearing caches..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Cache configuration for production
echo "⚙️  Caching configuration..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Link storage
echo "🔗 Linking storage..."
php artisan storage:link

# Set proper permissions
echo "🔒 Setting permissions..."
chmod -R 775 storage bootstrap/cache
chmod -R 775 storage/framework/
chmod -R 775 storage/logs/

# Run database migrations
# Uncomment the following line if you want to run migrations during deployment
# echo "🔄 Running database migrations..."
# php artisan migrate --force

echo "✅ Deployment completed successfully!"
