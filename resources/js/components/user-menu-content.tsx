import { DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuGroup } from '@/components/ui/dropdown-menu';
import { UserInfo } from '@/components/user-info';
import { useMobileNavigation } from '@/hooks/use-mobile-navigation';
import { type User } from '@/types';
import { Link, router } from '@inertiajs/react';
import { LogOut, Settings } from 'lucide-react';
import { ErrorBoundary } from 'react-error-boundary';


function ErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
    return (
        <div role="alert" className="p-4 text-sm text-red-600 dark:text-red-400">
            <p>Something went wrong:</p>
            <pre className="mt-2 overflow-auto rounded-md bg-neutral-100 p-2 text-xs dark:bg-neutral-800">
                {error.message}
            </pre>
            <button
                onClick={resetErrorBoundary}
                className="mt-2 rounded bg-blue-500 px-2 py-1 text-xs text-white hover:bg-blue-600"
            >
                Try again
            </button>
        </div>
    );
}

interface UserMenuContentProps {
    user: User;
}

function UserMenuContentInner({ user }: UserMenuContentProps) {
    const cleanup = useMobileNavigation();

    const handleLogout = (e: React.MouseEvent) => {
        e.preventDefault();
        try {
            cleanup();
            router.post(route('logout'));
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };

    return (
        <div className="w-56">
            <DropdownMenuLabel className="p-0 font-normal">
                <div className="flex items-center gap-2 px-2 py-1.5 text-left text-sm">
                    <UserInfo user={user} showEmail={true} />
                </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
                <DropdownMenuItem asChild>
                    <Link
                        href={route('profile.edit')}
                        className="flex w-full items-center px-2 py-1.5 text-sm text-left"
                        onClick={(e) => {
                            e.preventDefault();
                            cleanup();
                            router.visit(route('profile.edit'));
                        }}
                    >
                        <Settings className="mr-2 h-4 w-4" />
                        Profile Settings
                    </Link>
                </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
                <form method="POST" action={route('logout')} className="w-full">
                    <button 
                        type="submit" 
                        className="flex w-full items-center px-2 py-1.5 text-sm text-left"
                        onClick={handleLogout}
                    >
                        <LogOut className="mr-2 h-4 w-4" />
                        Log out
                    </button>
                </form>
            </DropdownMenuItem>
        </div>
    );
}

export function UserMenuContent({ user }: UserMenuContentProps) {
    return (
        <ErrorBoundary
            fallbackRender={({ error, resetErrorBoundary }) => (
                <ErrorFallback error={error} resetErrorBoundary={resetErrorBoundary} />
            )}
            onError={(error: Error) => {
                console.error('Error in UserMenuContent:', error);
            }}
        >
            <UserMenuContentInner user={user} />
        </ErrorBoundary>
    );
}
