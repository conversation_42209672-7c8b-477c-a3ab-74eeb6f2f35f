import { useSidebar } from '@/components/ui/sidebar';

export default function AppLogo() {
    const { state } = useSidebar();
    const isCollapsed = state === 'collapsed';
    
    return (
        <div className="flex w-full">
            {isCollapsed ? (
                <div className="flex h-8 w-8 items-center">
                    <img 
                        src="/images/icon-logo.png" 
                        alt="Logo" 
                        className="h-full w-full object-contain"
                    />
                </div>
            ) : (
                <div className="flex h-8 w-auto items-center">
                    <img 
                        src="/images/logo.png" 
                        alt="Logo" 
                        className="h-full w-auto" 
                    />
                </div>
            )}
        </div>
    );
}
