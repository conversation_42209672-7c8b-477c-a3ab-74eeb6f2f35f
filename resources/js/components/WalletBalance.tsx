import { Eye, EyeOff, Wallet } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';

export default function WalletBalance() {
    const [showBalance, setShowBalance] = useState(false);
    const balance = '₦ *********'; // Replace with actual balance from your backend
    
    return (
        <div className="flex items-center space-x-4 bg-red-pink-50 dark:bg-red-pink/20 rounded-lg px-4 py-2 border border-red-pink-100 dark:border-red-pink">
            <div className="flex items-center space-x-2">
                <Wallet className="h-5 w-5 text-red-pink dark:text-red-pink/80" />
                <span className="font-medium text-red-pink-800 dark:text-red-pink-200">Wallet Balance:</span>
                <span className="font-bold text-red-pink-700 dark:text-red-pink-300">
                    {showBalance ? '₦ 0.00' : balance}
                </span>
            </div>
            <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowBalance(!showBalance)}
                className="h-8 w-8 text-red-pink hover:bg-red-pink-100 dark:text-red-pink/80 dark:hover:bg-red-pink/30"
            >
                {showBalance ? (
                    <EyeOff className="h-4 w-4" />
                ) : (
                    <Eye className="h-4 w-4" />
                )}
                <span className="sr-only">
                    {showBalance ? 'Hide balance' : 'Show balance'}
                </span>
            </Button>
        </div>
    );
}
