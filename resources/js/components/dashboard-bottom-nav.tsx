import { Home, LayoutDashboard, Wallet, History } from 'lucide-react';
import { Link, usePage } from '@inertiajs/react';
import { cn } from '@/lib/utils';

interface NavItem {
    name: string;
    href: string;
    icon: React.ComponentType<{ className?: string }>;
    exact?: boolean;
}

const navItems: NavItem[] = [
    { name: 'Home', href: '/', icon: Home, exact: true },
    { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard, exact: true },
    { name: 'Wallet', href: '/wallet/fund', icon: Wallet },
    { name: 'History', href: '/history', icon: History }
];

export function DashboardBottomNav() {
    const { url } = usePage();

    const isActive = (item: NavItem) => {
        if (item.exact) {
            return url === item.href;
        }
        return url.startsWith(item.href) || url === item.href.replace(/\/$/, '');
    };

    return (
        <div className="fixed bottom-0 left-0 right-0 z-50 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 md:hidden">
            <nav className="flex items-center justify-around p-1">
                {navItems.map((item) => {
                    const active = isActive(item);
                    return (
                        <Link
                            key={item.name}
                            href={item.href}
                            className={cn(
                                'flex flex-col items-center justify-center p-2 text-xs font-medium w-full',
                                active ? 'text-red-pink' : 'text-muted-foreground hover:text-foreground',
                            )}
                            onClick={(e) => {
                                if (item.href === '#') {
                                    e.preventDefault();
                                    // Handle menu click (e.g., open a dropdown or sidebar)
                                }
                            }}
                        >
                            <div className={cn('p-1 rounded-full', active && 'border border-red-pink')}>
                                <item.icon className={cn('h-5 w-5', item.name === 'History' ? '' : active ? 'fill-current' : '')} />
                            </div>
                            <span className="text-[10px]">{item.name}</span>
                        </Link>
                    );
                })}
            </nav>
        </div>
    );
}
