import { LucideIcon } from 'lucide-react';
import { Link } from '@inertiajs/react';

interface ServiceIconProps {
    icon: LucideIcon;
    label: string;
    href: string;
}

export default function ServiceIcon({ icon: Icon, label, href }: ServiceIconProps) {
    return (
        <Link
            href={href}
            className="group flex flex-col items-center justify-center space-y-2 rounded-xl p-4 transition-all hover:bg-red-pink-50 dark:hover:bg-red-pink/20 hover:shadow-md hover:-translate-y-0.5"
        >
            <div className="flex h-14 w-14 items-center justify-center rounded-xl bg-red-pink-50 dark:bg-red-pink/20 p-2.5 group-hover:bg-red-pink-100 dark:group-hover:bg-red-pink/30 transition-colors">
                <Icon className="h-7 w-7 text-red-pink dark:text-red-pink/80" />
            </div>
            <span className="text-sm font-medium text-red-pink-800 dark:text-red-pink-200">{label}</span>
        </Link>
    );
}
