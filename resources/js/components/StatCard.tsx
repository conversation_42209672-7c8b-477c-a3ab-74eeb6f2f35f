interface StatCardProps {
    title: string;
    value: string | number;
    icon?: React.ReactNode;
}

export default function StatCard({ title, value, icon }: StatCardProps) {
    return (
        <div className="relative overflow-hidden rounded-xl border border-red-pink-100 bg-white dark:border-red-pink/50 dark:bg-red-pink/10 p-5 shadow-sm transition-all hover:shadow-md hover:-translate-y-0.5">
            <div className="absolute right-3 top-3 h-12 w-12 rounded-full bg-red-pink-100/50 dark:bg-red-pink/20"></div>
            <div className="relative z-10">
                <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-red-pink-800/80 dark:text-red-pink-200/80">{title}</h3>
                    {icon && <div className="text-red-pink dark:text-red-pink/80">{icon}</div>}
                </div>
                <p className="mt-2 text-2xl font-bold text-red-pink-800 dark:text-red-pink-100">{value}</p>
            </div>
        </div>
    );
}
