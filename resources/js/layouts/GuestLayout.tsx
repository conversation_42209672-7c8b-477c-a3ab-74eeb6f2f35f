import { ReactNode } from 'react';
import { Head } from '@inertiajs/react';

type Props = {
  children: ReactNode;
  title?: string;
};

export default function GuestLayout({ children, title }: Props) {
  return (
    <div className="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100">
      <Head>
        <title>{title || 'Welcome'}</title>
      </Head>

      <div className="w-full sm:max-w-md mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg">
        {children}
      </div>
    </div>
  );
}
