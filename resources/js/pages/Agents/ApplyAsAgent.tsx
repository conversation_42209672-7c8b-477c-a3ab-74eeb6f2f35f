import { Head, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// Textarea component is not available, using a regular textarea element instead
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { type BreadcrumbItem } from '@/types';

type ApplicationType = 'agent' | 'vendor';

type FormField = 
    | string 
    | number 
    | boolean 
    | File 
    | null 
    | undefined 
    | FormField[] 
    | { [key: string]: FormField };

interface FormData {
    [key: string]: FormField;
    business_name: string;
    business_address: string;
    phone_number: string;
    id_type: 'nin' | 'voter' | 'driver' | 'passport';
    id_number: string;
    id_document: File | null;
    business_document: File | null;
    experience: string;
    terms_accepted: boolean;
    type: ApplicationType;
}

export default function ApplyAsAgent() {
    const { data, setData, post, processing, errors } = useForm<FormData>({
        business_name: '',
        business_address: '',
        phone_number: '',
        id_type: 'nin',
        id_number: '',
        id_document: null,
        business_document: null,
        experience: '',
        terms_accepted: false,
        type: 'agent'
    } as FormData);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!data.terms_accepted) {
            toast.error('Please accept the terms and conditions');
            return;
        }
        
        const formData = new FormData();
        Object.entries(data).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                if (value instanceof File) {
                    formData.append(key, value);
                } else if (typeof value === 'boolean') {
                    formData.append(key, value ? '1' : '0');
                } else if (typeof value === 'string' || typeof value === 'number') {
                    formData.append(key, String(value));
                }
            }
        });

        // @ts-expect-error - Inertia's post type is too strict
        post(route('agent.apply.submit'), formData, {
            onSuccess: () => {
                toast.success('Application submitted successfully! We will review your application and get back to you soon.');
            },
            onError: () => {
                toast.error('Please check the form for errors');
            },
            preserveScroll: true,
        });
    };

    const handleFileChange = (field: 'id_document' | 'business_document') => 
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const file = e.target.files?.[0];
            if (file) {
                setData(field, file);
            }
        };
        
    const handleTextareaChange = (field: 'business_address' | 'experience') => 
        (e: React.ChangeEvent<HTMLTextAreaElement>) => {
            setData(field, e.target.value);
        };

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Become an Agent', href: '/agent/apply' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Apply as an Agent" />
            <div className="container py-8">
                <Card className="max-w-3xl mx-auto">
                    <CardHeader>
                        <CardTitle className="text-2xl font-bold">Become a Registered Agent</CardTitle>
                        <CardDescription>
                            Fill out the form below to apply as an agent. Our team will review your application and get back to you within 2-3 business days.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="business_name">Business Name *</Label>
                                    <Input
                                        id="business_name"
                                        value={data.business_name}
                                        onChange={(e) => setData('business_name', e.target.value)}
                                        required
                                    />
                                    {errors.business_name && <p className="text-sm text-red-500">{errors.business_name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="phone_number">Phone Number *</Label>
                                    <Input
                                        id="phone_number"
                                        type="tel"
                                        value={data.phone_number}
                                        onChange={(e) => setData('phone_number', e.target.value)}
                                        required
                                    />
                                    {errors.phone_number && <p className="text-sm text-red-500">{errors.phone_number}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="id_type">ID Type *</Label>
                                    <select
                                        id="id_type"
                                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        value={data.id_type}
                                        onChange={(e) => setData('id_type', e.target.value as 'nin' | 'voter' | 'driver' | 'passport')}
                                        required
                                    >
                                        <option value="nin">National ID (NIN)</option>
                                        <option value="voter">Voter's Card</option>
                                        <option value="driver">Driver's License</option>
                                        <option value="passport">International Passport</option>
                                    </select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="id_number">ID Number *</Label>
                                    <Input
                                        id="id_number"
                                        value={data.id_number}
                                        onChange={(e) => setData('id_number', e.target.value)}
                                        required
                                    />
                                    {errors.id_number && <p className="text-sm text-red-500">{errors.id_number}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="id_document">Upload ID Document *</Label>
                                    <Input
                                        id="id_document"
                                        type="file"
                                        accept="image/*,.pdf"
                                        onChange={handleFileChange('id_document')}
                                        required
                                    />
                                    <p className="text-xs text-muted-foreground">Upload a clear photo or scan of your ID (Max 5MB)</p>
                                    {errors.id_document && <p className="text-sm text-red-500">{errors.id_document}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="business_document">Business Registration (If any)</Label>
                                    <Input
                                        id="business_document"
                                        type="file"
                                        accept="image/*,.pdf"
                                        onChange={handleFileChange('business_document')}
                                    />
                                    <p className="text-xs text-muted-foreground">CAC registration or similar document</p>
                                    {errors.business_document && <p className="text-sm text-red-500">{errors.business_document}</p>}
                                </div>

                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="business_address">Business Address *</Label>
                                    <textarea
                                        id="business_address"
                                        value={data.business_address}
                                        onChange={handleTextareaChange('business_address')}
                                        className="flex min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        required
                                    />
                                    {errors.business_address && <p className="text-sm text-red-500">{errors.business_address}</p>}
                                </div>

                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="experience">Experience (Optional)</Label>
                                    <textarea
                                        id="experience"
                                        value={data.experience}
                                        onChange={handleTextareaChange('experience')}
                                        className="flex min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        placeholder="Tell us about your experience in this field..."
                                    />
                                </div>
                            </div>

                            <div className="flex items-start space-x-3">
                                <div className="flex items-center h-5">
                                    <input
                                        id="terms"
                                        type="checkbox"
                                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                                        checked={data.terms_accepted}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('terms_accepted', e.target.checked)}
                                        required
                                    />
                                </div>
                                <div className="text-sm leading-5">
                                    <label htmlFor="terms" className="font-medium text-foreground">
                                        I agree to the{' '}
                                        <a href="#" className="text-primary hover:underline">
                                            Terms and Conditions
                                        </a>{' '}
                                        and{' '}
                                        <a href="#" className="text-primary hover:underline">
                                            Privacy Policy
                                        </a>
                                    </label>
                                    <p className="text-muted-foreground">
                                        By submitting this application, you agree to our terms and that you have read our Privacy Policy.
                                    </p>
                                </div>
                            </div>

                            <div className="flex justify-end">
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Submitting...' : 'Submit Application'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                <div className="mt-8 max-w-3xl mx-auto">
                    <h3 className="text-lg font-medium mb-4">Why Become an Agent?</h3>
                    <div className="grid md:grid-cols-3 gap-4">
                        <div className="p-4 bg-muted/30 rounded-lg">
                            <h4 className="font-medium mb-2">Earn Commissions</h4>
                            <p className="text-sm text-muted-foreground">
                                Earn competitive commissions on every transaction you facilitate.
                            </p>
                        </div>
                        <div className="p-4 bg-muted/30 rounded-lg">
                            <h4 className="font-medium mb-2">Grow Your Business</h4>
                            <p className="text-sm text-muted-foreground">
                                Expand your service offerings and attract more customers.
                            </p>
                        </div>
                        <div className="p-4 bg-muted/30 rounded-lg">
                            <h4 className="font-medium mb-2">Dedicated Support</h4>
                            <p className="text-sm text-muted-foreground">
                                Get access to our dedicated agent support team.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
