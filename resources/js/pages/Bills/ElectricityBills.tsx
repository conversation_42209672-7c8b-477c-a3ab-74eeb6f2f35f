import { Head } from '@inertiajs/react';
import { useState } from 'react';
import { type BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Zap, Clock, ArrowRight, CreditCard, Loader2 } from 'lucide-react';

type Disco = 'ikeja-electric' | 'eko-electric' | 'kano-electric' | 'jos-electric' | 'ibadan-electric' | 'ph-electric' | 'kaduna-electric' | 'abuja-electric';

type MeterType = 'prepaid' | 'postpaid';


export default function ElectricityBills() {
    const [selectedDisco, setSelectedDisco] = useState<Disco>('ikeja-electric');
    const [meterNumber, setMeterNumber] = useState('');
    const [meterType, setMeterType] = useState<MeterType>('prepaid');
    const [amount, setAmount] = useState('');
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const [showInsufficientFunds, setShowInsufficientFunds] = useState(false);

    const discos = [
        { id: 'ikeja-electric', name: 'Ikeja Electric', logo: '/images/electricity/ikeja.png' },
        { id: 'eko-electric', name: 'Eko Electric', logo: '/images/electricity/eko.png' },
        { id: 'kano-electric', name: 'Kano Electric', logo: '/images/electricity/kano.jpg' },
        { id: 'jos-electric', name: 'Jos Electric', logo: '/images/electricity/jos.jpg' },
        { id: 'ibadan-electric', name: 'Ibadan Electric', logo: '/images/electricity/ibadan.jpeg' },
        { id: 'ph-electric', name: 'Port Harcourt Electric', logo: '/images/electricity/portharcourt.jpeg' },
        { id: 'kaduna-electric', name: 'Kaduna Electric', logo: '/images/electricity/kaduna.jpeg' },
        { id: 'abuja-electric', name: 'Abuja Electric', logo: '/images/electricity/abuja.png' },
    ];

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!meterNumber || !amount) {
            return;
        }

        setIsProcessing(true);
        
        try {
            // In a real app, you would fetch the actual wallet balance from your API
            // const response = await fetch('/api/wallet/balance');
            // const data = await response.json();
            // setWalletBalance(data.balance);
            
            // For demo purposes, we'll use a fixed balance and assume the amount is a number
            const paymentAmount = parseFloat(amount);
            const userBalance = 1000; // This would come from your API
            
            if (userBalance >= paymentAmount) {
                // Proceed with payment
                setShowPaymentModal(true);
            } else {
                // Show insufficient funds modal
                setShowInsufficientFunds(true);
            }
        } catch (error) {
            console.error('Error checking wallet balance:', error);
            // Handle error (show error message to user)
        } finally {
            setIsProcessing(false);
        }
    };

    const validateMeterNumber = (value: string) => {
        // Basic validation - adjust based on actual requirements
        return value.length >= 6 && value.length <= 20;
    };

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Bills', href: '/bills' },
        { title: 'Electricity', href: '/bills/electricity' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Electricity Bills" />
            <div className="container mx-auto px-4 py-8 max-w-5xl">
                <div className="text-center mb-8">
                    <h1 className="text-2xl font-bold text-gray-800">Electricity Bills</h1>
                    <p className="text-gray-600">Pay your electricity bills conveniently</p>
                </div>

                <div className="flex justify-center gap-4 mb-8 pt-4 overflow-x-auto pb-4">
                    {discos.map((disco) => (
                        <button
                            key={disco.id}
                            className={`w-24 h-24 rounded-xl flex flex-col items-center justify-center p-2 transition-all duration-200 flex-shrink-0 ${
                                selectedDisco === disco.id 
                                    ? 'ring-2 ring-red-pink bg-red-pink/10 scale-105 shadow-md' 
                                    : 'bg-white hover:bg-red-pink/5 hover:border-red-pink/30 border border-gray-200 hover:shadow-sm'
                            }`}
                            onClick={() => setSelectedDisco(disco.id as Disco)}
                        >
                            <div className="h-14 w-14 rounded-full bg-white p-1 mb-2 flex items-center justify-center">
                                <img 
                                    src={disco.logo} 
                                    alt={disco.name}
                                    className="h-10 w-10 object-contain rounded-full"
                                    onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.src = '/images/placeholder-logo.png';
                                    }}
                                />
                            </div>
                            <span className="text-xs font-medium text-gray-700 text-center">{disco.name.split(' ')[0]}</span>
                        </button>
                    ))}
                </div>

                <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-red-pink">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="space-y-2">
                            <Label htmlFor="disco">Electricity Company</Label>
                            <Select 
                                value={selectedDisco}
                                onValueChange={(value) => setSelectedDisco(value as Disco)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select Electricity Company" />
                                </SelectTrigger>
                                <SelectContent>
                                    {discos.map((disco) => (
                                        <SelectItem key={disco.id} value={disco.id}>
                                            {disco.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="meterNumber">Meter Number</Label>
                            <div className="relative">
                                <Zap className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <Input
                                    id="meterNumber"
                                    type="text"
                                    placeholder="Enter Meter Number"
                                    className="pl-10"
                                    value={meterNumber}
                                    onChange={(e) => setMeterNumber(e.target.value)}
                                    required
                                />
                            </div>
                            {meterNumber && !validateMeterNumber(meterNumber) && (
                                <p className="text-sm text-red-500">Meter number must be between 6-20 characters</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label>Meter Type</Label>
                            <div className="grid grid-cols-2 gap-4">
                                <button
                                    type="button"
                                    className={`p-4 border rounded-lg text-center transition-all ${
                                        meterType === 'prepaid'
                                            ? 'border-red-pink bg-red-pink/10 ring-2 ring-red-pink/20'
                                            : 'border-gray-200 hover:border-red-pink/50'
                                    }`}
                                    onClick={() => setMeterType('prepaid')}
                                >
                                    <h3 className="font-semibold text-gray-900">Prepaid</h3>
                                    <p className="text-sm text-gray-600 mt-1">Pay for electricity in advance</p>
                                </button>
                                <button
                                    type="button"
                                    className={`p-4 border rounded-lg text-center transition-all ${
                                        meterType === 'postpaid'
                                            ? 'border-red-pink bg-red-pink/10 ring-2 ring-red-pink/20'
                                            : 'border-gray-200 hover:border-red-pink/50'
                                    }`}
                                    onClick={() => setMeterType('postpaid')}
                                >
                                    <h3 className="font-semibold text-gray-900">Postpaid</h3>
                                    <p className="text-sm text-gray-600 mt-1">Pay after electricity usage</p>
                                </button>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="amount">Amount (₦)</Label>
                            <div className="relative">
                                <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <Input
                                    id="amount"
                                    type="number"
                                    placeholder="Enter amount"
                                    className="pl-10"
                                    value={amount}
                                    onChange={(e) => setAmount(e.target.value)}
                                    min={100}
                                    required
                                />
                            </div>
                            <p className="text-xs text-gray-500 mt-1">Minimum amount: ₦100</p>
                        </div>

                        {(meterNumber && amount) && (
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <h3 className="font-medium text-gray-900 mb-2">Order Summary</h3>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Electricity Company:</span>
                                        <span className="font-medium">
                                            {discos.find(d => d.id === selectedDisco)?.name}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Meter Number:</span>
                                        <span className="font-medium">{meterNumber}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Meter Type:</span>
                                        <span className="font-medium capitalize">{meterType}</span>
                                    </div>
                                    <div className="pt-2 mt-2 border-t border-gray-200 flex justify-between items-center">
                                        <span className="text-gray-600">Amount to Pay:</span>
                                        <span className="text-lg font-bold text-red-pink">₦{parseFloat(amount).toLocaleString()}</span>
                                    </div>
                                </div>
                            </div>
                        )}

                        <div className="pt-2">
                            <Button 
                                type="submit" 
                                className="w-full h-12 text-base bg-red-pink hover:bg-red-pink/90 transition-colors duration-200 flex items-center justify-center gap-2 text-white"
                                disabled={!meterNumber || !amount || !validateMeterNumber(meterNumber) || isProcessing}
                            >
                                {isProcessing ? (
                                    <>
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                        Processing...
                                    </>
                                ) : (
                                    <>
                                        Proceed to Pay
                                        <ArrowRight className="h-4 w-4" />
                                    </>
                                )}
                            </Button>
                        </div>
                    </form>

                    <div className="mt-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Recent Transactions</h3>
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <div className="flex items-center justify-center py-4 text-gray-500">
                                <Clock className="h-5 w-5 mr-2" />
                                <span>No recent transactions</span>
                            </div>
                        </div>
                    </div>

                    <div className="mt-6 text-center">
                        <a 
                            href="#" 
                            className="text-blue-600 text-sm flex items-center justify-center gap-1"
                        >
                            <Zap className="h-4 w-4" />
                            Check Electricity Status
                        </a>
                        <p className="text-xs text-gray-500 mt-1">View power availability in your area</p>
                    </div>
                </div>
            </div>
            
            {/* Payment Modal */}
            {/* Insufficient Funds Modal */}
            {showInsufficientFunds && (
                <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-xl p-6 w-full max-w-md">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">Insufficient Funds</h3>
                            <button 
                                onClick={() => setShowInsufficientFunds(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                ✕
                            </button>
                        </div>
                        
                        <div className="space-y-4">
                            <div className="bg-red-pink/10 p-4 rounded-lg text-center">
                                <p className="text-sm text-red-pink">Your wallet balance is insufficient for this transaction</p>
                                <p className="text-lg font-bold text-red-pink mt-2">₦{parseFloat(amount).toLocaleString()} needed</p>
                            </div>
                            
                            <p className="text-sm text-gray-600 text-center">
                                Please fund your wallet to complete this transaction.
                            </p>
                            
                            <div className="flex gap-3 pt-2">
                                <button
                                    onClick={() => setShowInsufficientFunds(false)}
                                    className="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
                                >
                                    Cancel
                                </button>
                                <a
                                    href="/wallet/fund"
                                    className="flex-1 py-3 px-4 bg-red-pink text-white rounded-lg font-medium hover:bg-red-pink/90 transition-colors text-center"
                                >
                                    Fund Wallet
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Payment Confirmation Modal */}
            {showPaymentModal && (
                <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-xl p-6 w-full max-w-md">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">Insufficient Balance</h3>
                            <button 
                                onClick={() => setShowPaymentModal(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                ✕
                            </button>
                        </div>
                        
                        <div className="space-y-4">
                            <div className="bg-red-pink/10 p-4 rounded-lg text-center border border-red-pink/20">
                                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-pink/10 mb-3">
                                    <Zap className="h-6 w-6 text-red-pink" />
                                </div>
                                <h3 className="text-lg font-medium text-red-pink">Insufficient Balance</h3>
                                <div className="mt-2">
                                    <p className="text-sm text-red-pink">
                                        Your current balance is not sufficient to complete this transaction.
                                    </p>
                                    <p className="mt-2 text-lg font-bold text-red-pink">
                                        ₦{parseFloat(amount).toLocaleString()} needed
                                    </p>
                                </div>
                            </div>
                            
                            <div className="text-sm text-gray-600 space-y-2">
                                <div className="flex justify-between">
                                    <span>Electricity Company:</span>
                                    <span className="font-medium">{discos.find(d => d.id === selectedDisco)?.name}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Meter Number:</span>
                                    <span className="font-medium">{meterNumber}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Meter Type:</span>
                                    <span className="font-medium capitalize">{meterType}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Amount:</span>
                                    <span className="font-medium text-red-pink">₦{parseFloat(amount).toLocaleString()}</span>
                                </div>
                            </div>
                            
                            <div className="flex gap-3 pt-2">
                                <button
                                    onClick={() => setShowPaymentModal(false)}
                                    className="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
                                >
                                    Cancel
                                </button>
                                <a
                                    href="/wallet/fund"
                                    className="flex-1 py-3 px-4 bg-red-pink text-white rounded-lg font-medium hover:bg-red-pink/90 transition-colors text-center flex items-center justify-center gap-2"
                                >
                                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Fund Wallet
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </AppLayout>
    );
}

// Add this to your app's routing configuration
// { path: '/bills/electricity', element: <ElectricityBills /> }
