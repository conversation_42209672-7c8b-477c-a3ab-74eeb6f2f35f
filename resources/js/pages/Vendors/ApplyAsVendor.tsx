import { Head, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// Using native textarea instead of shadcn Textarea component
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { type BreadcrumbItem } from '@/types';

// BusinessType is now defined inline in the FormData type

export default function ApplyAsVendor() {
    type FormData = {
        [key: string]: string | boolean | File | null | undefined;
        business_type: 'individual' | 'company';
        business_name: string;
        business_email: string;
        phone_number: string;
        business_address: string;
        business_description: string;
        website: string;
        tax_id: string;
        id_type: 'nin' | 'voter' | 'driver' | 'passport';
        id_number: string;
        id_document: File | null;
        business_document: File | null;
        terms_accepted: boolean;
    };

    const { data, setData, post, processing, errors = {} } = useForm<FormData>({
        business_type: 'individual',
        business_name: '',
        business_email: '',
        phone_number: '',
        business_address: '',
        business_description: '',
        website: '',
        tax_id: '',
        id_type: 'nin',
        id_number: '',
        id_document: null,
        business_document: null,
        terms_accepted: false,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!data.terms_accepted) {
            toast.error('Please accept the terms and conditions');
            return;
        }
        
        const formData = new FormData();
        
        // Append all form data
        Object.entries(data).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                if (value instanceof File) {
                    formData.append(key, value);
                } else if (typeof value === 'boolean') {
                    formData.append(key, value ? '1' : '0');
                } else if (typeof value === 'string' || typeof value === 'number') {
                    formData.append(key, String(value));
                }
            }
        });
        
        // Submit the form
        const formDataObj = Object.fromEntries(formData) as Record<string, string | File>;
        // @ts-expect-error - Inertia's post type is too strict
        post(route('vendor.apply.submit'), formDataObj, {
            onSuccess: () => {
                toast.success('Application submitted successfully! We will review your application and get back to you soon.');
            },
            onError: () => {
                toast.error('There was an error submitting your application. Please try again.');
            },
            preserveScroll: true,
            preserveState: true
        });
    };

    const handleFileChange = (field: 'id_document' | 'business_document') => 
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const file = e.target.files?.[0];
            if (file) {
                // Using type assertion to handle Inertia's type limitations
                setData(field, file as unknown as File);
            }
        };

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Become a Vendor', href: '/vendor/apply' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Apply as a Vendor" />
            <div className="container mx-auto px-4 py-8 max-w-4xl">
                <Card className="max-w-3xl mx-auto">
                    <CardHeader>
                        <CardTitle className="text-2xl font-bold">Become a Vendor</CardTitle>
                        <CardDescription>
                            Join our network of trusted vendors. Fill out the application form below to get started.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="space-y-4">
                                <div>
                                    <h4 className="font-medium mb-3">Business Type *</h4>
                                    <div className="grid grid-cols-2 gap-4">
                                        <label className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${data.business_type === 'individual' ? 'border-red-pink bg-red-pink/10' : 'border-gray-200 hover:border-red-pink/50'}`}>
                                            <div className="flex items-center space-x-3">
                                                <input
                                                    type="radio"
                                                    className="h-4 w-4 text-red-pink focus:ring-red-pink"
                                                    checked={data.business_type === 'individual'}
                                                    onChange={() => setData('business_type', 'individual')}
                                                />
                                                <div>
                                                    <p className="font-medium">Individual</p>
                                                    <p className="text-sm text-muted-foreground">Sole proprietor or individual seller</p>
                                                </div>
                                            </div>
                                        </label>
                                        <label className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${data.business_type === 'company' ? 'border-red-pink bg-red-pink/10' : 'border-gray-200 hover:border-red-pink/50'}`}>
                                            <div className="flex items-center space-x-3">
                                                <input
                                                    type="radio"
                                                    className="h-4 w-4 text-red-pink focus:ring-red-pink"
                                                    checked={data.business_type === 'company'}
                                                    onChange={() => setData('business_type', 'company')}
                                                />
                                                <div>
                                                    <p className="font-medium">Company</p>
                                                    <p className="text-sm text-muted-foreground">Registered business or organization</p>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="business_name">
                                            {data.business_type === 'company' ? 'Company Name' : 'Full Name'} *
                                        </Label>
                                        <Input
                                            id="business_name"
                                            value={data.business_name}
                                            onChange={(e) => setData('business_name', e.target.value)}
                                            required
                                        />
                                        {errors.business_name && <p className="text-sm text-red-500">{errors.business_name}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="business_email">Business Email *</Label>
                                        <Input
                                            id="business_email"
                                            type="email"
                                            value={data.business_email}
                                            onChange={(e) => setData('business_email', e.target.value)}
                                            required
                                        />
                                        {errors.business_email && <p className="text-sm text-red-500">{errors.business_email}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="phone_number">Phone Number *</Label>
                                        <Input
                                            id="phone_number"
                                            type="tel"
                                            value={data.phone_number}
                                            onChange={(e) => setData('phone_number', e.target.value)}
                                            required
                                        />
                                        {errors.phone_number && <p className="text-sm text-red-500">{errors.phone_number}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="website">Website (Optional)</Label>
                                        <Input
                                            id="website"
                                            type="url"
                                            value={data.website}
                                            onChange={(e) => setData('website', e.target.value)}
                                            placeholder="https://"
                                        />
                                        {errors.website && <p className="text-sm text-red-500">{errors.website}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="tax_id">Tax ID / VAT Number (Optional)</Label>
                                        <Input
                                            id="tax_id"
                                            value={data.tax_id}
                                            onChange={(e) => setData('tax_id', e.target.value)}
                                        />
                                        {errors.tax_id && <p className="text-sm text-red-500">{errors.tax_id}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="id_type">ID Type *</Label>
                                        <select
                                            id="id_type"
                                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-red-pink focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                            value={data.id_type}
                                            onChange={(e) => setData('id_type', e.target.value as 'nin' | 'voter' | 'driver' | 'passport')}
                                            required
                                        >
                                            <option value="nin">National ID (NIN)</option>
                                            <option value="voter">Voter's Card</option>
                                            <option value="driver">Driver's License</option>
                                            <option value="passport">International Passport</option>
                                        </select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="id_number">ID Number *</Label>
                                        <Input
                                            id="id_number"
                                            value={data.id_number}
                                            onChange={(e) => setData('id_number', e.target.value)}
                                            required
                                        />
                                        {errors.id_number && <p className="text-sm text-red-500">{errors.id_number}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="id_document">Upload ID Document *</Label>
                                        <Input
                                            id="id_document"
                                            type="file"
                                            accept="image/*,.pdf"
                                            onChange={handleFileChange('id_document')}
                                            required
                                        />
                                        <p className="text-xs text-muted-foreground">Upload a clear photo or scan of your ID (Max 5MB)</p>
                                        {errors.id_document && <p className="text-sm text-red-500">{errors.id_document}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="business_document">
                                            {data.business_type === 'company' ? 'Business Registration *' : 'Business Document (Optional)'}
                                        </Label>
                                        <Input
                                            id="business_document"
                                            type="file"
                                            accept="image/*,.pdf"
                                            onChange={handleFileChange('business_document')}
                                            required={data.business_type === 'company'}
                                        />
                                        <p className="text-xs text-muted-foreground">
                                            {data.business_type === 'company' 
                                                ? 'CAC registration or similar document' 
                                                : 'Any document that verifies your business (if any)'}
                                        </p>
                                        {errors.business_document && <p className="text-sm text-red-500">{errors.business_document}</p>}
                                    </div>

                                    <div className="space-y-2 md:col-span-2">
                                        <Label htmlFor="business_address">Business Address *</Label>
                                        <textarea
                                            id="business_address"
                                            value={data.business_address as string}
                                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setData('business_address', e.target.value)}
                                            className="flex min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-red-pink focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                            required
                                        />
                                        {errors.business_address && <p className="text-sm text-red-500">{errors.business_address}</p>}
                                    </div>

                                    <div className="space-y-2 md:col-span-2">
                                        <Label htmlFor="business_description">Business Description *</Label>
                                        <textarea
                                            id="business_description"
                                            value={data.business_description as string}
                                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setData('business_description', e.target.value)}
                                            className="flex min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-red-pink focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                            placeholder="Tell us about your business and the products/services you offer..."
                                            required
                                        />
                                        {errors.business_description && <p className="text-sm text-red-500">{errors.business_description}</p>}
                                    </div>
                                </div>
                                
                                <div className="flex items-start space-x-3 pt-4">
                                    <div className="flex items-center h-5">
                                        <input
                                            id="terms"
                                            type="checkbox"
                                            className="h-4 w-4 rounded border-gray-300 text-red-pink focus:ring-red-pink"
                                            checked={data.terms_accepted as boolean}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('terms_accepted', e.target.checked)}
                                            required
                                        />
                                    </div>
                                    <div className="text-sm leading-5">
                                        <label htmlFor="terms" className="font-medium text-foreground">
                                            I agree to the{' '}
                                            <a href="#" className="text-red-pink hover:underline">
                                                Vendor Terms and Conditions
                                            </a>{' '}
                                            and{' '}
                                            <a href="#" className="text-red-pink hover:underline">
                                                Privacy Policy
                                            </a>
                                        </label>
                                        <p className="text-muted-foreground">
                                            By submitting this application, you agree to our terms and that you have read our Privacy Policy.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="w-full">
                                <Button 
                                    type="submit" 
                                    className="w-full bg-red-pink hover:bg-red-pink/90 text-white"
                                    disabled={processing}
                                >
                                    {processing ? 'Submitting...' : 'Submit Application'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                <div className="mt-8 max-w-3xl mx-auto">
                    <h3 className="text-lg font-medium mb-4">Benefits of Becoming a Vendor</h3>
                    <div className="grid md:grid-cols-3 gap-4">
                        <div className="p-4 bg-muted/30 rounded-lg">
                            <h4 className="font-medium mb-2">Reach More Customers</h4>
                            <p className="text-sm text-muted-foreground">
                                Access our wide customer base and increase your sales.
                            </p>
                        </div>
                        <div className="p-4 bg-muted/30 rounded-lg">
                            <h4 className="font-medium mb-2">Easy Payments</h4>
                            <p className="text-sm text-muted-foreground">
                                Get paid securely and on time for your products/services.
                            </p>
                        </div>
                        <div className="p-4 bg-muted/30 rounded-lg">
                            <h4 className="font-medium mb-2">Vendor Support</h4>
                            <p className="text-sm text-muted-foreground">
                                Dedicated support to help you manage your vendor account.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
