import { Head } from '@inertiajs/react';
import { useState } from 'react';
import { type BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Types
type TVProvider = 'dstv' | 'gotv' | 'startimes';
type SubscriptionType = 'renewal' | 'new';

interface TVPlan {
  id: string;
  name: string;
  amount: number;
  duration: string;
}

export default function TVSubscription() {
  const [selectedProvider, setSelectedProvider] = useState<TVProvider>('dstv');
  const [selectedPlan, setSelectedPlan] = useState('');
  const [amount, setAmount] = useState('');
  const [subscriptionType, setSubscriptionType] = useState<SubscriptionType>('renewal');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [iucNumber, setIucNumber] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState(0);

  const providers = [
    { id: 'dstv', name: 'DSTV', logo: '/images/cables/DSTV.webp' },
    { id: 'gotv', name: 'GOtv', logo: '/images/cables/gotv.png' },
    { id: 'startimes', name: 'StarTimes', logo: '/images/cables/startime.jpg' },
  ];

  const plans: Record<TVProvider, TVPlan[]> = {
    dstv: [
      { id: 'dstv-padi', name: 'DStv Padi', amount: 2500, duration: 'Monthly' },
      { id: 'dstv-yanga', name: 'DStv Yanga', amount: 4500, duration: 'Monthly' },
      { id: 'dstv-confam', name: 'DStv Confam', amount: 12500, duration: 'Monthly' },
      { id: 'dstv-asian', name: 'DStv Asian', amount: 18500, duration: 'Monthly' },
    ],
    gotv: [
      { id: 'gotv-lite', name: 'GOtv Lite', amount: 1800, duration: 'Monthly' },
      { id: 'gotv-jolli', name: 'GOtv Jolli', amount: 3500, duration: 'Monthly' },
      { id: 'gotv-max', name: 'GOtv Max', amount: 5200, duration: 'Monthly' },
    ],
    startimes: [
      { id: 'startimes-nova', name: 'Nova', amount: 900, duration: 'Monthly' },
      { id: 'startimes-basic', name: 'Basic', amount: 1800, duration: 'Monthly' },
      { id: 'startimes-classic', name: 'Classic', amount: 3600, duration: 'Monthly' },
      { id: 'startimes-super', name: 'Super', amount: 5400, duration: 'Monthly' },
    ],
  };

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
    const plan = plans[selectedProvider].find(p => p.id === planId);
    if (plan) {
      setAmount(plan.amount.toString());
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!phoneNumber || !iucNumber || !selectedPlan) {
      return;
    }
    
    const selectedPlanData = plans[selectedProvider].find(p => p.id === selectedPlan);
    if (selectedPlanData) {
      setPaymentAmount(selectedPlanData.amount);
      setShowPaymentModal(true);
    }
  };
  



  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'TV', href: '/tv' },
    { title: 'TV Subscription', href: '/tv/subscription' },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Cable TV Subscription" />
      <div className="container mx-auto px-4 py-8 max-w-5xl">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-800">Cable TV Subscription</h1>
          <div className="mt-2 text-sm text-gray-600">
            <p>DSTV/GOtv Customer Care: 01-270-2888</p>
            <p>STARTIMES Customer Care: 01-270-3333</p>
          </div>
        </div>

        {/* Provider Logos */}
        <div className="flex justify-center gap-6 mb-8 pt-4 pb-6 overflow-x-auto">
          {providers.map((provider) => (
            <div 
              key={provider.id}
              className={`flex flex-col items-center cursor-pointer transition-all duration-200 ${
                selectedProvider === provider.id ? 'scale-105' : 'opacity-90 hover:opacity-100'
              }`}
              onClick={() => {
                setSelectedProvider(provider.id as TVProvider);
                setSelectedPlan('');
                setAmount('');
              }}
            >
              <div className={`w-20 h-20 rounded-full p-2 mb-2 transition-all duration-200 ${
                selectedProvider === provider.id 
                  ? 'bg-red-pink/10 ring-2 ring-red-pink shadow-md' 
                  : 'bg-white hover:bg-red-pink/5 hover:border-red-pink/30 border border-gray-200 hover:shadow-sm'
              }`}>
                <img 
                  src={provider.logo} 
                  alt={provider.name} 
                  className="w-full h-full object-contain rounded-full"
                />
              </div>
              <span className={`text-sm font-medium ${
                selectedProvider === provider.id ? 'text-red-pink font-semibold' : 'text-gray-700'
              }`}>
                {provider.name}
              </span>
            </div>
          ))}
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 max-w-5xl border-t-5 border-red-pink mx-auto">
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              {/* Provider Dropdown */}
              <div className="space-y-2">
                <Label htmlFor="provider">Provider</Label>
                <Select 
                  value={selectedProvider}
                  onValueChange={(value) => {
                    setSelectedProvider(value as TVProvider);
                    setSelectedPlan('');
                    setAmount('');
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Provider" />
                  </SelectTrigger>
                  <SelectContent>
                    {providers.map((provider) => (
                      <SelectItem key={provider.id} value={provider.id}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Plan Selection */}
              <div className="space-y-2">
                <Label>Plan</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {plans[selectedProvider].map((plan) => (
                    <div 
                      key={plan.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        selectedPlan === plan.id 
                          ? 'border-red-pink bg-red-pink/10 ring-1 ring-red-pink shadow-sm' 
                          : 'border-gray-200 hover:border-red-pink/50 hover:bg-red-pink/5'
                      }`}
                      onClick={() => handlePlanSelect(plan.id)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className={`font-medium ${
                            selectedPlan === plan.id ? 'text-red-pink' : 'text-gray-900'
                          }`}>
                            {plan.name}
                          </h4>
                          <p className="text-sm text-gray-500">{plan.duration}</p>
                        </div>
                        <span className={`font-semibold ${
                          selectedPlan === plan.id ? 'text-red-pink' : 'text-gray-900'
                        }`}>
                          ₦{plan.amount.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Amount to Pay */}
              <div className="space-y-2">
                <Label htmlFor="amount">Amount to Pay</Label>
                <Input
                  id="amount"
                  type="text"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="Amount to Pay"
                  className="w-full focus-visible:ring-red-pink focus-visible:ring-2 focus-visible:ring-offset-1"
                />
              </div>

              {/* Subscription Type */}
              <div className="space-y-2">
                <Label htmlFor="subscriptionType">Subscription Type</Label>
                <Select 
                  value={subscriptionType}
                  onValueChange={(value) => setSubscriptionType(value as SubscriptionType)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="renewal" className="focus:bg-red-pink/10">Renewal</SelectItem>
                    <SelectItem value="new" className="focus:bg-red-pink/10">New Subscription</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Customer Phone Number */}
              <div className="space-y-2">
                <Label htmlFor="phone">Customer Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  placeholder="Phone Number"
                  className="w-full focus-visible:ring-red-pink focus-visible:ring-2 focus-visible:ring-offset-1"
                />
              </div>

              {/* IUC Number */}
              <div className="space-y-2">
                <Label htmlFor="iuc">IUC Number</Label>
                <Input
                  id="iuc"
                  type="text"
                  value={iucNumber}
                  onChange={(e) => setIucNumber(e.target.value)}
                  placeholder="IUC Number"
                  className="w-full focus-visible:ring-red-pink focus-visible:ring-2 focus-visible:ring-offset-1"
                />
              </div>

              {/* Submit Button */}
              <Button 
                type="submit" 
                className="w-full py-6 text-lg font-medium mt-4 bg-red-pink hover:bg-red-pink/90 text-white transition-colors duration-200"
                disabled={!phoneNumber || !iucNumber || !selectedPlan}
              >
                Continue
              </Button>
            </div>
          </form>
        </div>
      </div>
      
      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Fund Your Account</h3>
              <button 
                onClick={() => setShowPaymentModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="bg-red-pink/10 p-4 rounded-lg text-center">
                <p className="text-sm text-gray-600">Amount to Pay</p>
                <p className="text-2xl font-bold text-red-pink">₦{paymentAmount.toLocaleString()}</p>
              </div>
              
              <p className="text-sm text-gray-600 text-center">
                Your account balance is insufficient. Please fund your account to complete this transaction.
              </p>
              
              <div className="flex gap-3 pt-2">
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <a
                  href="/wallet/fund"
                  className="flex-1 py-3 px-4 bg-red-pink text-white rounded-lg font-medium hover:bg-red-pink/90 transition-colors text-center"
                >
                  Fund Account
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </AppLayout>
  );
}
