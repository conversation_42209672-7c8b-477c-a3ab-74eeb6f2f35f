import { Head } from '@inertiajs/react';
import { useState } from 'react';
import { type BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Phone, Wifi } from 'lucide-react';

type Network = 'mtn' | 'airtel' | 'glo' | '9mobile';

export default function AirtimePurchase() {
    const [selectedNetwork, setSelectedNetwork] = useState<Network>('mtn');
    const [phoneNumber, setPhoneNumber] = useState('');
    const [amount, setAmount] = useState('');
    const [amountToPay, setAmountToPay] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const networks = [
        { id: 'mtn', name: 'MTN', logo: '/images/networks/mtn.png' },
        { id: 'airtel', name: 'Airtel', logo: '/images/networks/airtel.jpg' },
        { id: 'glo', name: 'Glo', logo: '/images/networks/glo.png' },
        { id: '9mobile', name: '9mobile', logo: '/images/networks/9mobile.jpg' },
    ];

    const handleAmountChange = (value: string) => {
        setAmount(value);
        // Calculate amount to pay (you can add discount logic here)
        setAmountToPay(value);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        // Handle form submission
        console.log({
            network: selectedNetwork,
            phoneNumber,
        });
        // Simulate API call
        setTimeout(() => {
            setIsLoading(false);
            // Show success message
        }, 2000);
    };

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Airtime', href: '/airtime' },
        { title: 'Buy Airtime', href: '/airtime/purchase' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Buy Airtime" />
            <div className="container mx-auto px-4 py-8 max-w-5xl">
                <div className="text-center mb-8">
                    <h1 className="text-2xl font-bold text-gray-800">Airtime For All Network</h1>
                    <p className="text-gray-600">Buy Airtime</p>
                </div>

                <div className="flex justify-center gap-4 mb-8">
                    {networks.map((network) => (
                        <button
                            key={network.id}
                            className={`w-24 h-24 rounded-xl flex flex-col items-center justify-center p-2 transition-all duration-200 ${
                                selectedNetwork === network.id 
                                    ? 'ring-2 ring-red-pink bg-red-pink/10 scale-105' 
                                    : 'bg-white hover:bg-gray-50 border border-gray-200'
                            }`}
                            onClick={() => setSelectedNetwork(network.id as Network)}
                        >
                            <div className="h-14 w-14 rounded-full bg-white p-1 mb-2 flex items-center justify-center">
                                <img 
                                    src={network.logo} 
                                    alt={network.name}
                                    className="h-10 w-10 object-contain rounded-full"
                                />
                            </div>
                            <span className="text-xs font-medium text-gray-700">{network.name}</span>
                        </button>
                    ))}
                </div>

                <div className="bg-white rounded-lg shadow-lg p-8 border-t-4 border-red-pink">
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="network">Network</Label>
                            <Select 
                                value={selectedNetwork}
                                onValueChange={(value) => setSelectedNetwork(value as Network)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select Network" />
                                </SelectTrigger>
                                <SelectContent>
                                    {networks.map((network) => (
                                        <SelectItem key={network.id} value={network.id}>
                                            {network.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <Label htmlFor="phone">Phone Number</Label>
                                <button 
                                    type="button" 
                                    className="text-sm text-blue-600 flex items-center gap-1"
                                >
<Wifi className="h-4 w-4" />
                                    Select From Contact
                                </button>
                            </div>
                            <div className="relative">
                                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <Input
                                    id="phone"
                                    type="tel"
                                    placeholder="Phone Number"
                                    className="pl-10"
                                    value={phoneNumber}
                                    onChange={(e) => setPhoneNumber(e.target.value)}
                                    required
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="type">Type</Label>
                            <Select defaultValue="vtu">
                                <SelectTrigger>
                                    <SelectValue placeholder="Select Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="vtu">VTU</SelectItem>
                                    <SelectItem value="share">Share & Sell</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="amount">Amount</Label>
                            <Input
                                id="amount"
                                type="number"
                                placeholder="Amount"
                                value={amount}
                                onChange={(e) => handleAmountChange(e.target.value)}
                                required
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="amountToPay">Amount To Pay</Label>
                            <Input
                                id="amountToPay"
                                type="number"
                                placeholder="Amount To Pay"
                                value={amountToPay}
                                readOnly
                            />
                        </div>

                        <div className="pt-4">
                            <Button 
                                type="submit" 
                                className="w-full h-12 text-base bg-red-pink hover:bg-red-pink/90 transition-colors duration-200 text-white"
                                disabled={isLoading}
                            >
                                {isLoading ? 'Processing...' : 'Buy Airtime'}
                            </Button>
                        </div>
                    </form>

                    <div className="mt-6 text-center">
                        <a 
                            href="#" 
                            className="text-blue-600 text-sm flex items-center justify-center gap-1"
                        >
                            <Wifi className="h-4 w-4" />
                            Check Network Status Before Purchase
                        </a>
                        <p className="text-xs text-gray-500 mt-1">View Network Status</p>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
