import { Head } from '@inertiajs/react';
import { useState } from 'react';
import { type BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowRight, Clock } from 'lucide-react';

type ExamType = 'waec' | 'neco' | 'nabteb' | 'jamb';

type ExamPinPlan = {
  id: string;
  name: string;
  amount: number;
  description: string;
};

export default function ExamPinPurchase() {
    const [selectedExam, setSelectedExam] = useState<ExamType>('waec');
    const [quantity, setQuantity] = useState('1');
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [phoneNumber, setPhoneNumber] = useState('');

    const examTypes = [
        { 
            id: 'waec', 
            name: 'WAEC', 
            logo: '/images/exams/waec.png',
            description: 'West African Examinations Council'
        },
        { 
            id: 'neco', 
            name: 'NECO', 
            logo: '/images/exams/neco.png',
            description: 'National Examinations Council'
        },
        { 
            id: 'nabteb', 
            name: 'NABTEB', 
            logo: '/images/exams/nabteb.jpg',
            description: 'National Business and Technical Examinations Board'
        },
        { 
            id: 'jamb', 
            name: 'JAMB', 
            logo: '/images/exams/jamb.jpg',
            description: 'Joint Admissions and Matriculation Board'
        },
    ];

    const examPlans: Record<ExamType, ExamPinPlan> = {
        waec: {
            id: 'waec',
            name: 'WAEC Result Checker',
            amount: 2500,
            description: 'Check your WAEC result with this PIN',
        },
        neco: {
            id: 'neco',
            name: 'NECO Result Checker',
            amount: 1200,
            description: 'Check your NECO result with this PIN',
        },
        nabteb: {
            id: 'nabteb',
            name: 'NABTEB Result Checker',
            amount: 1200,
            description: 'Check your NABTEB result with this PIN',
        },
        jamb: {
            id: 'jamb',
            name: 'JAMB e-PIN',
            amount: 5000,
            description: 'JAMB registration e-PIN',
        },
    };

    const calculateTotal = (qty: string) => {
        const qtyNum = parseInt(qty) || 1;
        return qtyNum * examPlans[selectedExam].amount;
    };

    const totalAmount = calculateTotal(quantity);

    const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        if (parseInt(value) >= 1 || value === '') {
            setQuantity(value);
        }
    };

    const handleExamChange = (value: string) => {
        setSelectedExam(value as ExamType);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!phoneNumber) return;
        setShowPaymentModal(true);
    };

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Exam Pins', href: '/exam-pins' },
        { title: 'Buy Exam PINs', href: '/exam-pins/purchase' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Exam PINs" />
            <div className="container mx-auto px-4 py-8 max-w-5xl">
                <div className="text-center mb-8">
                    <h1 className="text-2xl font-bold text-gray-800">Exam PINs</h1>
                    <p className="text-gray-600">Purchase examination PINs for various examination bodies</p>
                </div>

                <div className="flex justify-center gap-4 mb-8 pt-4 overflow-x-auto pb-4">
                    {examTypes.map((exam) => (
                        <button
                            key={exam.id}
                            className={`w-24 h-24 rounded-xl flex flex-col items-center justify-center p-2 transition-all duration-200 flex-shrink-0 ${
                                selectedExam === exam.id 
                                    ? 'ring-2 ring-red-pink bg-red-pink/10 scale-105 shadow-md' 
                                    : 'bg-white hover:bg-red-pink/5 hover:border-red-pink/30 border border-gray-200 hover:shadow-sm'
                            }`}
                            onClick={() => {
                                setSelectedExam(exam.id as ExamType);
                            }}
                        >
                            <div className="h-14 w-14 rounded-full bg-white p-1 mb-2 flex items-center justify-center">
                                <img 
                                    src={exam.logo} 
                                    alt={exam.name}
                                    className="h-10 w-10 object-contain rounded-full"
                                    onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.onerror = null;
                                        target.src = '/images/exams/default.png';
                                    }}
                                />
                            </div>
                            <span className="text-xs font-medium text-gray-700">{exam.name}</span>
                        </button>
                    ))}
                </div>

                <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-red-pink">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="space-y-2">
                            <Label htmlFor="examType">Exam Type</Label>
                            <Select 
                                value={selectedExam}
                                onValueChange={handleExamChange}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select Exam Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    {examTypes.map((exam) => (
                                        <SelectItem key={exam.id} value={exam.id}>
                                            {exam.name} - {exam.description}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="phone">Phone Number</Label>
                            <div className="relative">
                                <Input
                                    id="phone"
                                    type="tel"
                                    placeholder="Enter your phone number"
                                    className="pl-10"
                                    value={phoneNumber}
                                    onChange={(e) => setPhoneNumber(e.target.value)}
                                    required
                                />
                                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                    📱
                                </div>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="quantity">Quantity</Label>
                            <Input
                                id="quantity"
                                type="number"
                                min="1"
                                value={quantity}
                                onChange={handleQuantityChange}
                                className="w-32"
                                required
                            />
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h3 className="font-medium text-gray-900 mb-3">Order Summary</h3>
                            <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Exam:</span>
                                    <span className="font-medium">{examPlans[selectedExam].name}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Unit Price:</span>
                                    <span className="font-medium">₦{examPlans[selectedExam].amount.toLocaleString()}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Quantity:</span>
                                    <span className="font-medium">{quantity}</span>
                                </div>
                                <div className="pt-2 mt-2 border-t border-gray-200 flex justify-between items-center">
                                    <span className="text-gray-600">Total Amount:</span>
                                    <span className="text-lg font-bold text-red-pink">₦{totalAmount.toLocaleString()}</span>
                                </div>
                            </div>
                        </div>

                        <div className="pt-2">
                            <Button 
                                type="submit" 
                                className="w-full h-12 text-base bg-red-pink hover:bg-red-pink/90 transition-colors duration-200 flex items-center justify-center gap-2 text-white"
                                disabled={!phoneNumber}
                            >
                                Proceed to Pay
                                <ArrowRight className="h-4 w-4" />
                            </Button>
                        </div>
                    </form>

                    <div className="mt-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Recent Transactions</h3>
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <div className="flex items-center justify-center py-4 text-gray-500">
                                <Clock className="h-5 w-5 mr-2" />
                                <span>No recent transactions</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Payment Modal */}
            {showPaymentModal && (
                <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-xl p-6 w-full max-w-md">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">Fund Your Account</h3>
                            <button 
                                onClick={() => setShowPaymentModal(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                ✕
                            </button>
                        </div>
                        
                        <div className="space-y-4">
                            <div className="bg-red-pink/10 p-4 rounded-lg text-center">
                                <p className="text-sm text-gray-600">Amount to Pay</p>
                                <p className="text-2xl font-bold text-red-pink">₦{totalAmount.toLocaleString()}</p>
                            </div>
                            
                            <p className="text-sm text-gray-600 text-center">
                                Your account balance is insufficient. Please fund your account to complete this transaction.
                            </p>
                            
                            <div className="flex gap-3 pt-2">
                                <button
                                    onClick={() => setShowPaymentModal(false)}
                                    className="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
                                >
                                    Cancel
                                </button>
                                <a
                                    href="/wallet/fund"
                                    className="flex-1 py-3 px-4 bg-red-pink text-white rounded-lg font-medium hover:bg-red-pink/90 transition-colors text-center"
                                >
                                    Fund Account
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </AppLayout>
    );
}
