import { Bell, Plus, History, Phone, Wifi, Tv, FileText, BookOpen, CreditCard, UserCircle, BarChart2, Calendar, Wallet, TrendingUp, Gift } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Head, usePage } from '@inertiajs/react';
import { DashboardBottomNav } from '@/components/dashboard-bottom-nav';
import WalletBalance from '../components/WalletBalance';
import ServiceIcon from '../components/ServiceIcon';
import StatCard from '../components/StatCard';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard() {
    const { auth } = usePage<SharedData>().props;
    // Empty state for notifications
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 pb-20 md:pb-6 md:p-6">
                <DashboardBottomNav />
                {/* Header with user info and wallet */}
                <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-red-pink-900 dark:text-red-pink-100">Hi, {auth.user?.name || 'User'}</h1>
                        <p className="text-red-pink-600 dark:text-red-pink-400">Welcome back to your dashboard</p>
                    </div>
                    <div className="flex items-center space-x-3">
                        <WalletBalance />
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button 
                                    variant="outline" 
                                    size="icon" 
                                    className="h-10 w-10 border-red-pink-200 dark:border-red-pink-800 text-red-pink-700 dark:text-red-pink-300 hover:bg-red-pink-50 dark:hover:bg-red-pink-900/30 hover:border-red-pink-300 dark:hover:border-red-pink-700 relative"
                                >
                                    <Bell className="h-5 w-5" />
                                    <span className="sr-only">Notifications</span>
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="w-80 p-0" align="end">
                                <div className="p-4 border-b">
                                    <h3 className="font-semibold">Notifications</h3>
                                </div>
                                <div className="p-6 text-center">
                                    <Bell className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                    <p className="text-sm text-muted-foreground">No new notifications</p>
                                </div>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                    <a 
                        href="/wallet/fund"
                        className="flex w-full flex-col items-center justify-center gap-2 rounded-md bg-gradient-to-br from-red-pink to-red-pink/90 px-4 py-4 text-white shadow-md transition-all duration-200 hover:from-red-pink/90 hover:to-red-pink/80 hover:shadow-lg"
                    >
                        <Plus className="h-6 w-6" />
                        <span>Add Money</span>
                    </a>
                    <a 
                        href="/history"
                        className="flex w-full flex-col items-center justify-center gap-2 rounded-md border border-red-pink-200 bg-white px-4 py-4 text-red-pink-700 shadow-sm transition-all duration-200 hover:border-red-pink-300 hover:bg-red-pink-50 hover:text-red-pink-800 dark:border-red-pink-800 dark:bg-card dark:text-red-pink-300 dark:hover:border-red-pink-700 dark:hover:bg-red-pink-900/30"
                    >
                        <History className="h-6 w-6" />
                        <span>History</span>
                    </a>
                </div>

                {/* Services Grid */}
                <div className="rounded-lg border bg-card p-4 shadow-sm">
                    <h2 className="mb-4 text-lg font-semibold">Services</h2>
                    <div className="grid grid-cols-4 gap-4">
                        <ServiceIcon icon={Phone} label="Airtime" href="/airtime" />
                        <ServiceIcon icon={Wifi} label="Data" href="/data" />
                        <ServiceIcon icon={Tv} label="TV" href="/tv" />
                        <ServiceIcon icon={FileText} label="Bills" href="/bills/electricity" />
                        <ServiceIcon icon={BookOpen} label="Exam Pin" href="/exam-pins" />
                        <ServiceIcon icon={CreditCard} label="Recharge Pin" href="/recharge-pin" />
                        <ServiceIcon icon={UserCircle} label="Agent" href="/agent/apply" />
                        <ServiceIcon icon={UserCircle} label="Vendor" href="/vendor/apply" />
                    </div>
                </div>

                {/* Statistics */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <h2 className="col-span-full text-lg font-semibold">Statistics</h2>
                    <StatCard 
                        title="Total Transactions" 
                        value="0" 
                        icon={<BarChart2 className="h-5 w-5" />} 
                    />
                    <StatCard 
                        title="Amount Spent This Week" 
                        value="₦0" 
                        icon={<Calendar className="h-5 w-5" />} 
                    />
                    <StatCard 
                        title="Amount Spent This Month" 
                        value="₦0" 
                        icon={<Calendar className="h-5 w-5" />} 
                    />
                    <StatCard 
                        title="Total Spent" 
                        value="₦0" 
                        icon={<Wallet className="h-5 w-5" />} 
                    />
                    <StatCard 
                        title="Total Funding" 
                        value="₦0" 
                        icon={<TrendingUp className="h-5 w-5" />} 
                    />
                    <StatCard 
                        title="CashBack Bonus" 
                        value="₦0" 
                        icon={<Gift className="h-5 w-5" />} 
                    />
                </div>
            </div>
        </AppLayout>
    );
}
