import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';

export default function FundWallet() {
    const [isPaying, setIsPaying] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [preview, setPreview] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileSelect = (file: File) => {
        // Check file type
        const validTypes = ['image/jpeg', 'image/png', 'application/pdf'];
        if (!validTypes.includes(file.type)) {
            toast.error('Please upload a valid file type (JPEG, PNG, or PDF)');
            return;
        }

        // Check file size (5MB max)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
            toast.error('File size must be less than 5MB');
            return;
        }

        setSelectedFile(file);
        
        // Create preview for images
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreview(reader.result as string);
            };
            reader.readAsDataURL(file);
        } else {
            setPreview('pdf');
        }
    };
    const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
    const [amount, setAmount] = useState('');
    const [activeTab, setActiveTab] = useState('bank'); // 'bank' or 'card'

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!amount || isNaN(Number(amount)) || Number(amount) < 100) {
            toast.error('Please enter a valid amount (minimum ₦100)');
            return;
        }

        setIsPaying(true);
        setPaymentStatus('processing');

        try {
            const response = await fetch(route('payment.initialize'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ amount: parseFloat(amount) * 100 }), // Convert to kobo
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                // Redirect to Monnify payment page
                window.location.href = result.data.checkout_url;
            } else {
                throw new Error(result.message || 'Failed to initialize payment');
            }
        } catch (error) {
            console.error('Payment error:', error);
            setPaymentStatus('error');
            toast.error(error instanceof Error ? error.message : 'Failed to process payment');
        } finally {
            setIsPaying(false);
        }
    };

    // Check for payment status in URL params
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const status = urlParams.get('status');
        
        if (status === 'success') {
            toast.success('Payment successful! Your wallet has been credited.');
        } else if (status === 'failed') {
            toast.error('Payment failed. Please try again.');
        }
    }, []);

    return (
        <AppLayout>
            <Head title="Fund Wallet" />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6 max-w-4xl mx-auto w-full">
                <div className="flex flex-col items-center justify-center gap-3 py-8 text-center">
                    <div className="flex h-16 w-16 items-center justify-center rounded-full bg-red-pink/10">
                        <ArrowUp className="h-8 w-8 text-red-pink" />
                    </div>
                    <h1 className="text-2xl font-bold text-red-pink">FUND WALLET</h1>
                </div>

                <Tabs 
                    defaultValue="bank" 
                    className="w-full"
                >
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="bank" onClick={() => setActiveTab('bank')} className={activeTab === 'bank' ? 'bg-muted' : ''}>
                            Bank
                        </TabsTrigger>
                        <TabsTrigger value="card" onClick={() => setActiveTab('card')} className={activeTab === 'card' ? 'bg-muted' : ''}>
                            Card
                        </TabsTrigger>
                        <TabsTrigger value="manual" onClick={() => setActiveTab('manual')} className={activeTab === 'manual' ? 'bg-muted' : ''}>
                            Manual
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="bank" className="mt-6">
                        <div className="space-y-6">
                            <div className="rounded-lg border bg-card p-6">
                                <h3 className="text-lg font-medium mb-4">Fund Your Wallet</h3>
                                
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div>
                                        <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Amount (₦)
                                        </label>
                                        <Input
                                            id="amount"
                                            type="number"
                                            value={amount}
                                            onChange={(e) => setAmount(e.target.value)}
                                            placeholder="Enter amount"
                                            min="100"
                                            step="100"
                                            className="w-full"
                                            required
                                        />
                                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Minimum: ₦100</p>
                                    </div>

                                    <Button 
                                        type="submit"
                                        className="w-full bg-red-pink hover:bg-red-pink/90 text-white"
                                        disabled={isPaying}
                                    >
                                        {isPaying ? (
                                            <>
                                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                Processing...
                                            </>
                                        ) : (
                                            'Proceed to Payment'
                                        )}
                                    </Button>
                                </form>

                                {paymentStatus === 'processing' && (
                                    <div className="flex items-center justify-center gap-2 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-blue-600 dark:text-blue-400 animate-spin">
                                            <rect width="20" height="12" x="2" y="6" rx="2"/>
                                            <circle cx="12" cy="12" r="2"/>
                                            <path d="M6 12h.01M18 12h.01"/>
                                        </svg>
                                        <span className="text-sm text-muted-foreground">
                                            Redirecting to payment gateway...
                                        </span>
                                    </div>
                                )}

                                <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-shield-check">
                                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/>
                                        <path d="m9 12 2 2 4-4"/>
                                    </svg>
                                    <span>Secure payment powered by Monnify</span>
                                </div>
                            </div>

                            <div className="rounded-lg border bg-card p-6 space-y-4">
                                <h3 className="font-medium text-foreground">Payment Methods</h3>
                                <div className="space-y-3">
                                    <button 
                                        type="button" 
                                        onClick={(e) => {
                                            e.preventDefault();
                                            setActiveTab('card');
                                            const cardTab = document.querySelector('[value="card"]') as HTMLElement;
                                            if (cardTab) cardTab.click();
                                            window.scrollTo({ top: 0, behavior: 'smooth' });
                                        }}
                                        className={`w-full text-left focus:outline-none ${activeTab === 'card' ? 'ring-2 ring-red-pink rounded-lg' : ''}`}
                                    >
                                        <div className={`flex items-center gap-3 p-3 ${activeTab === 'card' ? 'bg-red-pink-50 dark:bg-red-pink-900/20' : 'bg-muted/50'} rounded-lg hover:bg-muted/70 transition-colors`}>
                                            <div className="p-2 bg-primary/10 rounded-md">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-primary">
                                                    <rect width="20" height="12" x="2" y="6" rx="2"/>
                                                    <circle cx="12" cy="12" r="2"/>
                                                    <path d="M6 12h.01M18 12h.01"/>
                                                </svg>
                                            </div>
                                            <div className="flex-1">
                                                <h4 className="font-medium">Card Payment</h4>
                                                <p className="text-xs text-muted-foreground">Visa, Mastercard, Verve</p>
                                            </div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-muted-foreground">
                                                <path d="m9 18 6-6-6-6"/>
                                            </svg>
                                        </div>
                                    </button>
                                    
                                    <button 
                                        type="button" 
                                        onClick={(e) => {
                                            e.preventDefault();
                                            setActiveTab('bank');
                                            const bankTab = document.querySelector('[value="bank"]') as HTMLElement;
                                            if (bankTab) bankTab.click();
                                            window.scrollTo({ top: 0, behavior: 'smooth' });
                                        }}
                                        className={`w-full text-left focus:outline-none ${activeTab === 'bank' ? 'ring-2 ring-red-pink rounded-lg' : ''}`}
                                    >
                                        <div className={`flex items-center gap-3 p-3 ${activeTab === 'bank' ? 'bg-red-pink-50 dark:bg-red-pink-900/20' : 'bg-muted/50'} rounded-lg hover:bg-muted/70 transition-colors`}>
                                            <div className="p-2 bg-primary/10 rounded-md">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-primary">
                                                    <rect width="20" height="12" x="2" y="6" rx="2"/>
                                                    <circle cx="12" cy="12" r="2"/>
                                                    <path d="M6 12h.01M18 12h.01"/>
                                                </svg>
                                            </div>
                                            <div className="flex-1">
                                                <h4 className="font-medium">Bank Transfer</h4>
                                                <p className="text-xs text-muted-foreground">Make transfer to our account</p>
                                            </div>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-muted-foreground">
                                                <path d="m9 18 6-6-6-6"/>
                                            </svg>
                                        </div>
                                    </button>
                                </div>
                            </div>

                            <div className="rounded-lg border bg-card p-6 space-y-4">
                                <h3 className="font-medium text-foreground">Need Help?</h3>
                                <p className="text-sm text-muted-foreground">
                                    Having issues with your payment? Contact our support team for assistance.
                                </p>
                                <Button variant="outline" className="w-full sm:w-auto">
                                    Contact Support
                                </Button>
                            </div>
                        </div>
                    </TabsContent>

                    <TabsContent value="card" className="mt-6">
                        <div className="space-y-6">
                            <div className="rounded-lg border bg-card p-6">
                                <h3 className="text-lg font-medium mb-4">Card Details</h3>
                                
                                <form className="space-y-4">
                                    <div>
                                        <label htmlFor="card-amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Amount (₦)
                                        </label>
                                        <Input
                                            id="card-amount"
                                            type="number"
                                            value={amount}
                                            onChange={(e) => setAmount(e.target.value)}
                                            placeholder="Enter amount"
                                            min="100"
                                            step="100"
                                            className="w-full"
                                            required
                                        />
                                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Minimum: ₦100</p>
                                    </div>

                                    <div>
                                        <label htmlFor="card-number" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Card Number
                                        </label>
                                        <div className="relative">
                                            <Input
                                                id="card-number"
                                                type="text"
                                                placeholder="1234 5678 9012 3456"
                                                className="pl-10 text-lg font-mono tracking-widest"
                                                maxLength={19}
                                                required
                                            />
                                            <svg 
                                                xmlns="http://www.w3.org/2000/svg" 
                                                width="20" 
                                                height="20" 
                                                viewBox="0 0 24 24" 
                                                fill="none" 
                                                stroke="currentColor" 
                                                strokeWidth="2" 
                                                strokeLinecap="round" 
                                                strokeLinejoin="round"
                                                className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
                                            >
                                                <rect width="20" height="14" x="2" y="5" rx="2"/>
                                                <line x1="2" x2="22" y1="10" y2="10"/>
                                            </svg>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label htmlFor="card-expiry" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Expiry Date
                                            </label>
                                            <Input
                                                id="card-expiry"
                                                type="text"
                                                placeholder="MM/YY"
                                                className="font-mono"
                                                maxLength={5}
                                                required
                                            />
                                        </div>
                                        <div>
                                            <label htmlFor="card-cvv" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                CVV
                                            </label>
                                            <div className="relative">
                                                <Input
                                                    id="card-cvv"
                                                    type="text"
                                                    placeholder="123"
                                                    className="font-mono"
                                                    maxLength={4}
                                                    required
                                                />
                                                <svg 
                                                    xmlns="http://www.w3.org/2000/svg" 
                                                    width="20" 
                                                    height="20" 
                                                    viewBox="0 0 24 24" 
                                                    fill="none" 
                                                    stroke="currentColor" 
                                                    strokeWidth="2" 
                                                    strokeLinecap="round" 
                                                    strokeLinejoin="round"
                                                    className="h-4 w-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
                                                >
                                                    <rect width="20" height="14" x="2" y="5" rx="2"/>
                                                    <path d="M2 10h20"/>
                                                    <path d="M7 15h1"/>
                                                    <path d="M12 15h1"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <label htmlFor="card-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Name on Card
                                        </label>
                                        <Input
                                            id="card-name"
                                            type="text"
                                            placeholder="John Doe"
                                            required
                                        />
                                    </div>

                                    <div className="pt-2">
                                        <Button 
                                            type="submit" 
                                            className="w-full h-12 text-base bg-red-pink hover:bg-red-pink/90 text-white"
                                            disabled={isPaying || !amount || isNaN(Number(amount)) || Number(amount) < 100}
                                        >
                                            {isPaying ? (
                                                <>
                                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                    Processing...
                                                </>
                                            ) : (
                                                `Pay ₦${amount || '0'}`
                                            )}
                                        </Button>
                                    </div>

                                    <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-shield-check">
                                            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/>
                                            <path d="m9 12 2 2 4-4"/>
                                        </svg>
                                        <span>Secure payment processed by our payment partner</span>
                                    </div>
                                </form>
                            </div>

                            <div className="flex items-center gap-3 p-4 bg-red-pink/5 border border-red-pink/20 rounded-lg">
                                <div className="flex-shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-red-pink">
                                        <circle cx="12" cy="12" r="10"/>
                                        <line x1="12" x2="12" y1="16" y2="12"/>
                                        <line x1="12" x2="12.01" y1="8" y2="8"/>
                                    </svg>
                                </div>
                                <p className="text-sm text-foreground">
                                    Your card details are encrypted and processed securely. We do not store your card information.
                                </p>
                            </div>
                        </div>
                    </TabsContent>

                    <TabsContent value="manual" className="mt-6">
                        <div className="space-y-6">
                            <div className="rounded-lg border bg-card p-6">
                                <div className="text-center mb-6">
                                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-pink/10 mb-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-8 w-8 text-red-pink">
                                            <rect width="20" height="12" x="2" y="6" rx="2"/>
                                            <circle cx="12" cy="12" r="2"/>
                                            <path d="M6 12h.01M18 12h.01"/>
                                        </svg>
                                    </div>
                                    <h3 className="text-lg font-medium mb-2">Manual Bank Transfer</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Transfer money to our account and upload proof of payment for verification.
                                    </p>
                                </div>

                                <div className="space-y-6">
                                    <div className="space-y-4">
                                        <h4 className="font-medium text-foreground">Bank Details</h4>
                                        <div className="space-y-3">
                                            <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                                                <span className="text-sm text-muted-foreground">Bank Name</span>
                                                <span className="font-medium">Access Bank</span>
                                            </div>
                                            <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                                                <span className="text-sm text-muted-foreground">Account Name</span>
                                                <span className="font-medium">Ezekiel Miracle Precious</span>
                                            </div>
                                            <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                                                <span className="text-sm text-muted-foreground">Account Number</span>
                                                <div className="flex items-center gap-2">
                                                    <span className="font-mono font-medium">**********</span>
                                                    <button 
                                                        onClick={async (e) => {
                                                            e.preventDefault();
                                                            e.stopPropagation();
                                                            try {
                                                                await navigator.clipboard.writeText('**********');
                                                                toast.success('Account number copied to clipboard!');
                                                            } catch (err) {
                                                                console.error('Failed to copy:', err);
                                                                // Fallback for browsers that don't support clipboard API
                                                                const textArea = document.createElement('textarea');
                                                                textArea.value = '**********';
                                                                document.body.appendChild(textArea);
                                                                textArea.select();
                                                                try {
                                                                    const successful = document.execCommand('copy');
                                                                    if (successful) {
                                                                        toast.success('Account number copied!');
                                                                    } else {
                                                                        throw new Error('Copy command was unsuccessful');
                                                                    }
                                                                } catch (err) {
                                                                    console.error('Fallback copy failed:', err);
                                                                    toast.error('Failed to copy account number');
                                                                } finally {
                                                                    document.body.removeChild(textArea);
                                                                }
                                                            }
                                                        }}
                                                        className="text-blue-600 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md p-1"
                                                        title="Copy account number to clipboard"
                                                        type="button"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="space-y-4">
                                        <h4 className="font-medium text-foreground">Upload Proof of Payment</h4>
                                        <div className="space-y-4">
                                            {!preview ? (
                                                <div 
                                                    onDragOver={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                    }}
                                                    onDrop={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        const file = e.dataTransfer.files[0];
                                                        if (file) handleFileSelect(file);
                                                    }}
                                                    onClick={() => fileInputRef.current?.click()}
                                                    className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/30 hover:bg-muted/50 transition-colors"
                                                >
                                                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                                        <svg className="w-8 h-8 mb-2 text-muted-foreground" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                                            <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                                        </svg>
                                                        <p className="mb-1 text-sm text-muted-foreground">
                                                            <span className="font-semibold text-blue-600 hover:text-blue-700">Click to upload</span> or drag and drop
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">
                                                            PNG, JPG, or PDF (MAX. 5MB)
                                                        </p>
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="relative group">
                                                    <div className="relative w-full h-48 bg-muted/30 rounded-lg overflow-hidden">
                                                        {selectedFile?.type.startsWith('image/') ? (
                                                            <img 
                                                                src={preview} 
                                                                alt="Preview" 
                                                                className="w-full h-full object-contain"
                                                            />
                                                        ) : (
                                                            <div className="flex flex-col items-center justify-center h-full p-4">
                                                                <svg className="w-12 h-12 text-muted-foreground mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                    <rect width="20" height="12" x="2" y="6" rx="2"/>
                                                                    <circle cx="12" cy="12" r="2"/>
                                                                    <path d="M6 12h.01M18 12h.01"/>
                                                                </svg>
                                                                <p className="text-sm font-medium text-center text-muted-foreground truncate w-full px-2">
                                                                    {selectedFile?.name}
                                                                </p>
                                                                <p className="text-xs text-muted-foreground">
                                                                    {selectedFile && (selectedFile.size / 1024 / 1024).toFixed(2)} MB
                                                                </p>
                                                            </div>
                                                        )}
                                                    </div>
                                                    <button
                                                        type="button"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            setSelectedFile(null);
                                                            setPreview(null);
                                                            if (fileInputRef.current) {
                                                                fileInputRef.current.value = '';
                                                            }
                                                        }}
                                                        className="absolute -top-2 -right-2 bg-red-pink-500 text-white rounded-full p-1.5 shadow-md hover:bg-red-pink-600 focus:outline-none focus:ring-2 focus:ring-red-pink-500 focus:ring-offset-2 transition-colors"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            )}
                                            <input 
                                                ref={fileInputRef}
                                                id="dropzone-file" 
                                                type="file" 
                                                className="hidden" 
                                                accept=".png,.jpg,.jpeg,.pdf" 
                                                onChange={(e) => {
                                                    if (e.target.files && e.target.files[0]) {
                                                        handleFileSelect(e.target.files[0]);
                                                    }
                                                }}
                                            />
                                        </div>
                                    </div>

                                    <div className="pt-2">
                                        <Button 
                                            type="submit" 
                                            className="w-full h-12 text-base bg-red-pink hover:bg-red-pink/90 text-white"
                                            disabled={isPaying}
                                        >
                                            {isPaying ? (
                                                <>
                                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                    Submitting...
                                                </>
                                            ) : (
                                                'Submit Proof of Payment'
                                            )}
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="rounded-lg border border-red-pink/20 bg-red-pink/5 p-4">
                                <div className="flex">
                                    <div className="flex-shrink-0">
                                        <svg className="h-5 w-5 text-red-pink" viewBox="0 0 20 20" fill="currentColor">
                                            <path fillRule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-red-pink">Important Notice</h3>
                                        <div className="mt-2 text-sm text-foreground/80">
                                            <p>• Your account will be credited within 1-2 hours after verification.</p>
                                            <p>• Please ensure the transaction reference matches your account details.</p>
                                            <p>• For any issues, contact our support team.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}
