import { Head, useForm, usePage } from '@inertiajs/react';
import { useState } from 'react';
// Remove motion animations temporarily to fix TypeScript errors
import { User } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Camera, Loader2, Trash2, AlertCircle, User as UserIcon, AlertTriangle, Lock } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';

interface Props {
    user: User;
}

export default function ProfileEdit({ user }: Props) {
    const { flash } = usePage<{ flash?: { success?: string } }>().props;
    
    const [photoPreview, setPhotoPreview] = useState<string | null>(null);

    // Define the shape of our form data
    type FormDataValue = string | number | boolean | File | Blob | null | undefined;
    
    interface ProfileFormData {
        name: string;
        email: string;
        photo: File | null | string; // Can be File, null, or string (URL)
        [key: string]: FormDataValue; // For Inertia.js compatibility
    }

    interface PasswordFormData {
        current_password: string;
        password: string;
        password_confirmation: string;
        [key: string]: FormDataValue; // For Inertia.js compatibility
    }

    // Initialize profile form with proper types and type assertion for Inertia.js
    const profileForm = useForm<ProfileFormData>({
        name: user.name,
        email: user.email,
        photo: null
    });
    
    // Initialize password form
    const passwordForm = useForm<PasswordFormData>({
        current_password: '',
        password: '',
        password_confirmation: ''
    });
    
    // Destructure form methods for easier access
    const { data, setData, put, processing, errors } = profileForm;
    const { data: passwordData, setData: setPasswordData, put: updatePassword, processing: updatingPassword, errors: passwordErrors } = passwordForm;

    const { data: deleteData, setData: setDeleteData, post: postDelete, processing: deleteProcessing } = useForm({
        password: '',
    });

    const submitProfile = (e: React.FormEvent) => {
        e.preventDefault();
        const formData = new FormData();
        
        // Only include the name if it has changed
        if (data.name !== user.name) {
            formData.append('name', data.name);
        }
        
        // Only include the photo if a new one was selected
        if (data.photo && data.photo instanceof File) {
            formData.append('photo', data.photo);
        }
        
        // If no fields to update, show a message and return
        if (formData.entries().next().done) {
            // No fields to update
            return;
        }
        
        const options = {
            onSuccess: () => {
                // Clear photo preview after successful upload
                if (data.photo) {
                    setPhotoPreview(null);
                    // Reset the photo field to null after successful upload
                    setData('photo', null);
                }
            },
            preserveScroll: true,
            forceFormData: true as const,
            onError: (errors: Record<string, string>) => {
                // Handle any errors from the server
                console.error('Error updating profile:', errors);
            },
        };
        
        // @ts-expect-error - forceFormData is a valid option in Inertia.js
        put(route('profile.update'), formData, options);
    };

    const submitPassword = (e: React.FormEvent) => {
        e.preventDefault();
        
        updatePassword(route('password.update'), {
            onSuccess: () => {
                // Clear the form on success
                setPasswordData({
                    current_password: '',
                    password: '',
                    password_confirmation: ''
                });
            },
            preserveScroll: true,
        });
    };
    
    const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            // Update the form data with the new file
            setData('photo', file);
            // Create a preview of the image
            const reader = new FileReader();
            reader.onload = () => {
                if (reader.result) {
                    setPhotoPreview(reader.result.toString());
                }
            };
            reader.readAsDataURL(file);
            // Reset the input value to allow selecting the same file again
            e.target.value = '';
        }
    };
    
    const deleteAccount = (e: React.FormEvent) => {
        e.preventDefault();
        if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
            postDelete(route('profile.destroy'), {
                onBefore: () => confirm('This will permanently delete your account. Are you sure?'),
                preserveScroll: true,
            });
        }
    };
    
    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map(part => part[0])
            .join('')
            .toUpperCase()
            .substring(0, 2);
    };

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Profile', href: route('profile.edit') },
        { title: 'Edit Profile', href: '#' }
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Profile" />
            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    <div className="max-w-4xl mx-auto">
                        {flash && 'success' in flash && flash.success && (
                            <div className="mb-8">
                                <Alert className="bg-green-50 border-green-200 text-green-800 shadow-lg">
                                    <AlertCircle className="h-5 w-5" />
                                    <div>
                                        <AlertTitle className="font-medium">Success</AlertTitle>
                                        <AlertDescription className="text-sm">{flash.success as string}</AlertDescription>
                                    </div>
                                </Alert>
                            </div>
                        )}
                
                    <div className="space-y-8">
                    </div>
                    
                    {/* Profile Photo Section */}
                    <Card className="overflow-hidden border border-gray-100 dark:border-gray-800 shadow-sm hover:shadow-md my-4 transition-shadow duration-300">
                        <CardHeader className="pb-3 border-b border-gray-100 dark:border-gray-800">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                                    <UserIcon className="h-5 w-5" />
                                </div>
                                <div>
                                    <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">Profile Photo</CardTitle>
                                    <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
                                        Update your profile photo
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="pt-6">
                            <div className="flex flex-col sm:flex-row items-center space-y-6 sm:space-y-0 sm:space-x-8">
                                <div className="relative group">
                                    <Avatar className="h-32 w-32 border-4 border-white dark:border-gray-800 shadow-lg">
                                        {photoPreview ? (
                                            <img 
                                                src={photoPreview} 
                                                alt={data.name || 'User'}
                                                className="object-cover h-full w-full"
                                            />
                                        ) : data.photo ? (
                                            <AvatarImage 
                                                src={data.photo instanceof File ? URL.createObjectURL(data.photo) : ''} 
                                                alt={data.name || 'User'}
                                                className="object-cover h-full w-full"
                                            />
                                        ) : (
                                            <AvatarImage 
                                                src={user.profile_photo_url?.toString() || ''} 
                                                alt={data.name?.toString() || 'User'}
                                                className="object-cover h-full w-full"
                                            />
                                        )}
                                        <AvatarFallback className="text-2xl font-medium bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                                            {data.name ? getInitials(data.name) : '??'}
                                        </AvatarFallback>
                                    </Avatar>
                                    <label 
                                        className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-full transition-opacity opacity-0 group-hover:opacity-100"
                                    >
                                        <div 
                                            className="bg-white/90 text-gray-900 rounded-full p-2 shadow-lg hover:scale-105 active:scale-95 transition-transform"
                                        >
                                            <Camera className="h-5 w-5" />
                                        </div>
                                        <input 
                                            type="file" 
                                            className="hidden" 
                                            accept="image/*"
                                            onChange={handlePhotoChange}
                                        />
                                    </label>
                                </div>
                                
                                <div className="space-y-3 text-center sm:text-left">
                                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">{data.name}</h3>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">{data.email}</p>
                                    
                                    <div className="pt-2">
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            JPG, GIF or PNG. Max size of 5MB
                                        </p>
                                        {errors.photo && (
                                            <p className="mt-1 text-xs text-red-600 flex items-center justify-center sm:justify-start">
                                                <AlertCircle className="h-3.5 w-3.5 mr-1" />
                                                {errors.photo}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    {/* Profile Information Section */}
                    <Card className="overflow-hidden border border-gray-100 dark:border-gray-800 shadow-sm hover:shadow-md my-4 transition-shadow duration-300">
                        <CardHeader className="pb-3 border-b border-gray-100 dark:border-gray-800">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400">
                                    <UserIcon className="h-5 w-5" />
                                </div>
                                <div>
                                    <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">Profile Information</CardTitle>
                                    <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
                                        Update your account's profile information and email address
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="pt-6">
                            <form onSubmit={submitProfile} className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-1">
                                        <Label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">Full Name</Label>
                                        <Input
                                            id="name"
                                            type="text"
                                            className={cn(
                                                "mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 transition duration-200",
                                                errors.name && "border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500"
                                            )}
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            required
                                            autoComplete="name"
                                        />
                                        {errors.name && (
                                            <p className="mt-1 text-sm text-red-600 flex items-center">
                                                <AlertCircle className="h-3.5 w-3.5 mr-1" />
                                                {errors.name}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-1">
                                        <Label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-300">Email Address</Label>
                                        <div className="relative">
                                            <Input
                                                id="email"
                                                type="email"
                                                className={cn(
                                                    "mt-1 block w-full rounded-lg border-gray-300 bg-gray-50 text-gray-600 shadow-sm cursor-not-allowed",
                                                    errors.email && "border-red-300 text-red-900 placeholder-red-300"
                                                )}
                                                value={data.email}
                                                readOnly
                                                disabled
                                                autoComplete="email"
                                            />
                                            <div className="absolute inset-0 bg-white/30 dark:bg-gray-800/30 rounded-lg" />
                                        </div>
                                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                            Contact support to change your email address
                                        </p>
                                        {errors.email && (
                                            <p className="mt-1 text-sm text-red-600 flex items-center">
                                                <AlertCircle className="h-3.5 w-3.5 mr-1" />
                                                {errors.email}
                                            </p>
                                        )}
                                    </div>
                                </div>

                                <div className="flex justify-end pt-2">
                                    <Button 
                                        type="submit" 
                                        disabled={processing}
                                        className="px-6 py-2.5 bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 text-white font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-200 transform hover:-translate-y-0.5"
                                    >
                                        {processing ? (
                                            <>
                                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                <span>Saving Changes</span>
                                            </>
                                        ) : (
                                            <span>Save Changes</span>
                                        )}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                    
                    {/* Update Password Section */}
                    <Card className="overflow-hidden border border-gray-100 dark:border-gray-800 shadow-sm my-4 hover:shadow-md transition-shadow duration-300">
                        <CardHeader className="pb-3 border-b border-gray-100 dark:border-gray-800">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400">
                                    <Lock className="h-5 w-5" />
                                </div>
                                <div>
                                    <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">Update Password</CardTitle>
                                    <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
                                        Ensure your account is using a long, random password to stay secure
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="pt-6">
                            <form onSubmit={submitPassword} className="space-y-6">
                                <div className="space-y-5">
                                    <div className="space-y-1">
                                        <Label htmlFor="current_password" className="text-sm font-medium text-gray-700 dark:text-gray-300">Current Password</Label>
                                        <Input
                                            id="current_password"
                                            type="password"
                                            className={cn(
                                                "mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 transition duration-200",
                                                passwordErrors.current_password && "border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500"
                                            )}
                                            value={passwordData.current_password}
                                            onChange={(e) => setPasswordData('current_password', e.target.value)}
                                            autoComplete="current-password"
                                        />
                                        {passwordErrors.current_password && (
                                            <p className="mt-1 text-sm text-red-600 flex items-center">
                                                <AlertCircle className="h-3.5 w-3.5 mr-1" />
                                                {passwordErrors.current_password}
                                            </p>
                                        )}
                                    </div>
                                    
                                    <div className="space-y-1">
                                        <Label htmlFor="password" className="text-sm font-medium text-gray-700 dark:text-gray-300">New Password</Label>
                                        <Input
                                            id="password"
                                            type="password"
                                            className={cn(
                                                "mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 transition duration-200",
                                                passwordErrors.password && "border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500"
                                            )}
                                            value={passwordData.password}
                                            onChange={(e) => setPasswordData('password', e.target.value)}
                                            autoComplete="new-password"
                                        />
                                        {passwordErrors.password && (
                                            <p className="mt-1 text-sm text-red-600 flex items-center">
                                                <AlertCircle className="h-3.5 w-3.5 mr-1" />
                                                {passwordErrors.password}
                                            </p>
                                        )}
                                    </div>
                                    
                                    <div className="space-y-1">
                                        <Label htmlFor="password_confirmation" className="text-sm font-medium text-gray-700 dark:text-gray-300">Confirm New Password</Label>
                                        <Input
                                            id="password_confirmation"
                                            type="password"
                                            className={cn(
                                                "mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 transition duration-200",
                                                passwordErrors.password_confirmation && "border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500"
                                            )}
                                            value={passwordData.password_confirmation}
                                            onChange={(e) => setPasswordData('password_confirmation', e.target.value)}
                                            autoComplete="new-password"
                                        />
                                        {passwordErrors.password_confirmation && (
                                            <p className="mt-1 text-sm text-red-600 flex items-center">
                                                <AlertCircle className="h-3.5 w-3.5 mr-1" />
                                                {passwordErrors.password_confirmation}
                                            </p>
                                        )}
                                    </div>
                                </div>

                                <div className="flex justify-end pt-2">
                                    <Button 
                                        type="submit" 
                                        disabled={updatingPassword}
                                        className="px-6 py-2.5 bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 text-white font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-200 transform hover:-translate-y-0.5"
                                    >
                                        {updatingPassword ? (
                                            <>
                                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                <span>Updating Password</span>
                                            </>
                                        ) : (
                                            <span>Update Password</span>
                                        )}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                    
                    {/* Delete Account Section */}
                    <Card className="border-red-100 dark:border-red-900/30 bg-gradient-to-br from-red-50 to-red-50/50 dark:from-red-900/10 dark:to-red-900/5 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
                        <CardHeader className="pb-3 border-b border-red-100 dark:border-red-900/30">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400">
                                    <AlertTriangle className="h-5 w-5" />
                                </div>
                                <div>
                                    <CardTitle className="text-lg font-semibold text-red-700 dark:text-red-400">Delete Account</CardTitle>
                                    <CardDescription className="text-sm text-red-600/80 dark:text-red-400/80">
                                        Once your account is deleted, all of its resources and data will be permanently deleted.
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="pt-6">
                            <form onSubmit={deleteAccount} className="space-y-6">
                                <div className="space-y-8">
                                    <p className="text-sm text-red-700/90 dark:text-red-300/90 mb-4">
                                        Please enter your password to confirm you would like to permanently delete your account. 
                                        <span className="font-medium">This action cannot be undone.</span>
                                    </p>
                                    <div className="max-w-md">
                                        <Input
                                            id="current_password"
                                            type="password"
                                            className="mt-1 block w-full"
                                            value={deleteData.password as string}
                                            onChange={(e) => setDeleteData('password', e.target.value)}
                                            required
                                            autoComplete="current-password"
                                        />
                                    </div>
                                </div>
                                
                                <div className="flex justify-end pt-2">
                                    <Button 
                                        type="submit" 
                                        variant="destructive"
                                        disabled={!deleteData.password || deleteProcessing}
                                        className={cn(
                                            "px-6 py-2.5 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-200 transform hover:-translate-y-0.5",
                                            (!deleteData.password || deleteProcessing) && "opacity-70 cursor-not-allowed"
                                        )}
                                    >
                                        {deleteProcessing ? (
                                            <>
                                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                <span>Deleting Account</span>
                                            </>
                                        ) : (
                                            <>
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                <span>Delete Account</span>
                                            </>
                                        )}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
                </div>
            </div>
        </AppLayout>
    );
}
