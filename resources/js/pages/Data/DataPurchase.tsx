import { Head } from '@inertiajs/react';
import { useState } from 'react';
import { type BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Phone, Wifi, Clock, ArrowRight } from 'lucide-react';

type Network = 'mtn' | 'airtel' | 'glo' | '9mobile';
type DataPlan = {
  id: string;
  name: string;
  amount: number;
  validity: string;
  data: string;
};

export default function DataPurchase() {
    const [selectedNetwork, setSelectedNetwork] = useState<Network>('mtn');
    const [phoneNumber, setPhoneNumber] = useState('');
    const [selectedPlan, setSelectedPlan] = useState<string>('');
    const [showPaymentModal, setShowPaymentModal] = useState(false);

    const networks = [
        { id: 'mtn', name: 'MTN', logo: '/images/networks/mtn.png' },
        { id: 'airtel', name: 'Airtel', logo: '/images/networks/airtel.jpg' },
        { id: 'glo', name: 'Glo', logo: '/images/networks/glo.png' },
        { id: '9mobile', name: '9mobile', logo: '/images/networks/9mobile.jpg' },
    ];

    const dataPlans: Record<Network, DataPlan[]> = {
        mtn: [
            { id: 'mtn-1', name: 'Daily', amount: 200, validity: '1 Day', data: '1.5GB' },
            { id: 'mtn-2', name: 'Weekly', amount: 500, validity: '7 Days', data: '3.5GB' },
            { id: 'mtn-3', name: 'Monthly', amount: 1500, validity: '30 Days', data: '10GB' },
        ],
        airtel: [
            { id: 'airtel-1', name: 'Daily', amount: 200, validity: '1 Day', data: '1GB' },
            { id: 'airtel-2', name: 'Weekly', amount: 500, validity: '7 Days', data: '2.5GB' },
            { id: 'airtel-3', name: 'Monthly', amount: 1500, validity: '30 Days', data: '8GB' },
        ],
        glo: [
            { id: 'glo-1', name: 'Daily', amount: 200, validity: '1 Day', data: '1.2GB' },
            { id: 'glo-2', name: 'Weekly', amount: 500, validity: '7 Days', data: '3GB' },
            { id: 'glo-3', name: 'Monthly', amount: 1500, validity: '30 Days', data: '9GB' },
        ],
        '9mobile': [
            { id: '9mobile-1', name: 'Daily', amount: 200, validity: '1 Day', data: '1GB' },
            { id: '9mobile-2', name: 'Weekly', amount: 500, validity: '7 Days', data: '2.5GB' },
            { id: '9mobile-3', name: 'Monthly', amount: 1500, validity: '30 Days', data: '8GB' },
        ],
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!phoneNumber || !selectedPlan) {
            return;
        }
        
        setShowPaymentModal(true);
    };

    const selectedPlanData = dataPlans[selectedNetwork].find(plan => plan.id === selectedPlan);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Data', href: '/data' },
        { title: 'Buy Data', href: '/data/purchase' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Buy Data" />
            <div className="container mx-auto px-4 py-8 max-w-5xl">
                <div className="text-center mb-8">
                    <h1 className="text-2xl font-bold text-gray-800">Data Plans</h1>
                    <p className="text-gray-600">Buy data for all networks</p>
                </div>

                <div className="flex justify-center gap-4 mb-8 pt-4 overflow-x-auto pb-4">
                    {networks.map((network) => (
                        <button
                            key={network.id}
                            className={`w-24 h-24 rounded-xl flex flex-col items-center justify-center p-2 transition-all duration-200 flex-shrink-0 ${
                                selectedNetwork === network.id 
                                    ? 'ring-2 ring-red-pink bg-red-pink/10 scale-105 shadow-md' 
                                    : 'bg-white hover:bg-red-pink/5 hover:border-red-pink/30 border border-gray-200 hover:shadow-sm'
                            }`}
                            onClick={() => {
                                setSelectedNetwork(network.id as Network);
                                setSelectedPlan('');
                            }}
                        >
                            <div className="h-14 w-14 rounded-full bg-white p-1 mb-2 flex items-center justify-center">
                                <img 
                                    src={network.logo} 
                                    alt={network.name}
                                    className="h-10 w-10 object-contain rounded-full"
                                />
                            </div>
                            <span className="text-xs font-medium text-gray-700">{network.name}</span>
                        </button>
                    ))}
                </div>

                <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-red-pink">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="space-y-2">
                            <Label htmlFor="network">Network</Label>
                            <Select 
                                value={selectedNetwork}
                                onValueChange={(value) => {
                                    setSelectedNetwork(value as Network);
                                    setSelectedPlan('');
                                }}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select Network" />
                                </SelectTrigger>
                                <SelectContent>
                                    {networks.map((network) => (
                                        <SelectItem key={network.id} value={network.id}>
                                            {network.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <Label htmlFor="phone">Phone Number</Label>
                                <button 
                                    type="button" 
                                    className="text-sm text-blue-600 flex items-center gap-1"
                                >
                                    <Wifi className="h-4 w-4" />
                                    Select From Contact
                                </button>
                            </div>
                            <div className="relative">
                                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <Input
                                    id="phone"
                                    type="tel"
                                    placeholder="Phone Number"
                                    className="pl-10"
                                    value={phoneNumber}
                                    onChange={(e) => setPhoneNumber(e.target.value)}
                                    required
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label>Select Data Plan</Label>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                {dataPlans[selectedNetwork].map((plan) => (
                                    <button
                                        key={plan.id}
                                        type="button"
                                        className={`p-4 border rounded-lg text-left transition-all ${
                                            selectedPlan === plan.id
                                                ? 'border-red-pink bg-red-pink/10 ring-2 ring-red-pink/20'
                                                : 'border-gray-200 hover:border-red-pink/50'
                                        }`}
                                        onClick={() => setSelectedPlan(plan.id)}
                                    >
                                        <div className="flex justify-between items-start">
                                            <div>
                                                <h3 className="font-semibold text-gray-900">{plan.name}</h3>
                                                <p className="text-2xl font-bold text-red-pink">₦{plan.amount}</p>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm text-gray-500">{plan.validity}</p>
                                            </div>
                                        </div>
                                        <div className="mt-2 flex items-center text-sm text-gray-600">
                                            <Wifi className="h-4 w-4 mr-1" />
                                            <span>{plan.data}</span>
                                        </div>
                                    </button>
                                ))}
                            </div>
                        </div>

                        {selectedPlanData && (
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <h3 className="font-medium text-gray-900 mb-2">Order Summary</h3>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Network:</span>
                                        <span className="font-medium">
                                            {networks.find(n => n.id === selectedNetwork)?.name}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Phone Number:</span>
                                        <span className="font-medium">{phoneNumber || '--'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Plan:</span>
                                        <span className="font-medium">{selectedPlanData.name} - {selectedPlanData.data}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Validity:</span>
                                        <span className="font-medium">{selectedPlanData.validity}</span>
                                    </div>
                                    <div className="pt-2 mt-2 border-t border-gray-200 flex justify-between items-center">
                                        <span className="text-gray-600">Total Amount:</span>
                                        <span className="text-lg font-bold text-red-pink">₦{selectedPlanData.amount}</span>
                                    </div>
                                </div>
                            </div>
                        )}

                        <div className="pt-2">
                            <Button 
                                type="submit" 
                                className="w-full h-12 text-base bg-red-pink hover:bg-red-pink/90 transition-colors duration-200 flex items-center justify-center gap-2 text-white"
                                disabled={!selectedPlan || !phoneNumber}
                            >
                                Proceed to Pay
                                <ArrowRight className="h-4 w-4" />
                            </Button>
                        </div>
                    </form>

                    <div className="mt-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Recent Transactions</h3>
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <div className="flex items-center justify-center py-4 text-gray-500">
                                <Clock className="h-5 w-5 mr-2" />
                                <span>No recent transactions</span>
                            </div>
                        </div>
                    </div>

                    <div className="mt-6 text-center">
                        <a 
                            href="#" 
                            className="text-blue-600 text-sm flex items-center justify-center gap-1"
                        >
                            <Wifi className="h-4 w-4" />
                            Check Network Status Before Purchase
                        </a>
                        <p className="text-xs text-gray-500 mt-1">View Network Status</p>
                    </div>
                </div>
            </div>
            
            {/* Payment Modal */}
            {showPaymentModal && selectedPlanData && (
                <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-xl p-6 w-full max-w-md">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">Fund Your Account</h3>
                            <button 
                                onClick={() => setShowPaymentModal(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                ✕
                            </button>
                        </div>
                        
                        <div className="space-y-4">
                            <div className="bg-red-pink/10 p-4 rounded-lg text-center">
                                <p className="text-sm text-gray-600">Amount to Pay</p>
                                <p className="text-2xl font-bold text-red-pink">₦{selectedPlanData.amount.toLocaleString()}</p>
                            </div>
                            
                            <p className="text-sm text-gray-600 text-center">
                                Your account balance is insufficient. Please fund your account to complete this transaction.
                            </p>
                            
                            <div className="flex gap-3 pt-2">
                                <button
                                    onClick={() => setShowPaymentModal(false)}
                                    className="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
                                >
                                    Cancel
                                </button>
                                <a
                                    href="/wallet/fund"
                                    className="flex-1 py-3 px-4 bg-red-pink text-white rounded-lg font-medium hover:bg-red-pink/90 transition-colors text-center"
                                >
                                    Fund Account
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </AppLayout>
    );
}
