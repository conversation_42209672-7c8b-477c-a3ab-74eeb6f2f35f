import React, { useState, useEffect, useRef } from 'react';
import { Head, Link, usePage, router } from '@inertiajs/react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { User, LogOut, LayoutDashboard, Settings } from 'lucide-react';

// Define the user type
type User = {
    id: number;
    name: string;
    email: string;
    email_verified_at?: string | null;
    created_at?: string;
    updated_at?: string;
};

// Define the auth type
type Auth = {
    user: User | null;
};

// Define the page props type
type PageProps = {
    auth: Auth;
};

// Hamburger icon component
const MenuIcon = ({ isOpen }: { isOpen: boolean }) => (
  <div className="space-y-1.5">
    <span className={`block h-0.5 w-6 bg-gray-700 transition-transform ${isOpen ? 'rotate-45 translate-y-2' : ''}`}></span>
    <span className={`block h-0.5 w-6 bg-gray-700 transition-opacity ${isOpen ? 'opacity-0' : 'opacity-100'}`}></span>
    <span className={`block h-0.5 w-6 bg-gray-700 transition-transform ${isOpen ? '-rotate-45 -translate-y-2' : ''}`}></span>
  </div>
);


// Testimonial data
const testimonials = [
    {
        id: 1,
        name: 'Adebayo Ojo',
        role: 'Small Business Owner',
        quote: 'Reliable and Fast Service',
        content: 'vita-minde has transformed how I manage my business. Buying airtime and data has never been easier. The platform is reliable and the transactions are instant. Highly recommended!',
        image: '/images/user.png'
    },
    {
        id: 2,
        name: 'Chioma Eze',
        role: 'Frequent User',
        quote: 'Best Rates in the Market',
        content: 'I\'ve tried many platforms, but vita-minde offers the best rates for airtime and data. The user interface is clean and straightforward. Customer support is also very responsive.',
        image: '/images/user.png'
    },
    {
        id: 3,
        name: 'Emeka Nwankwo',
        role: 'Agent',
        quote: 'Game Changer for My Business',
        content: 'As an agent, I rely on vita-minde for all my airtime and data needs. The platform is stable, transactions are instant, and the commission structure is very competitive. It has significantly boosted my business.',
        image: '/images/user.png'
    }
];

export default function Welcome() {
    const { auth } = usePage<PageProps>().props;
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
    const [currentTestimonial, setCurrentTestimonial] = useState(0);
    const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(0);
    const testimonialRef = useRef<HTMLDivElement>(null);

    const toggleFaq = (index: number) => {
        setOpenFaqIndex(openFaqIndex === index ? null : index);
    };

    const nextTestimonial = () => {
        setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    };

    const prevTestimonial = () => {
        setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    };

    // Auto-advance testimonials every 5 seconds
    useEffect(() => {
        const timer = setInterval(() => {
            nextTestimonial();
        }, 5000);
        return () => clearInterval(timer);
    }, []);
    return (
        <div className="min-h-screen bg-gray-50">
            <Head title="vita-minde - Your One-stop Platform For All Bills Payment" />
            
            {/* Header */}
            <header className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
                <div className="container mx-auto px-6 py-4">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-2">
                            <img src="/images/logo.png" className='h-auto w-36 md:w-32 lg:w-40 xl:w-48 sm:w-36' alt="vita-minde Logo" />
                            {/* <h1 className="text-2xl md:text-3xl font-black text-gray-900 tracking-tight">vita-minde</h1> */}
                        </div>
                        
                        {/* Desktop Navigation */}
                        <nav className="hidden md:flex items-center space-x-6">
                            <a 
                                href="#home" 
                                className="text-gray-700 hover:text-red-pink transition-colors px-2 py-1 font-medium"
                                onClick={(e) => {
                                    e.preventDefault();
                                    document.getElementById('home')?.scrollIntoView({ behavior: 'smooth' });
                                }}
                            >
                                Home
                            </a>
                            <a 
                                href="#about" 
                                className="text-gray-700 hover:text-red-pink transition-colors px-2 py-1 font-medium"
                                onClick={(e) => {
                                    e.preventDefault();
                                    document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' });
                                }}
                            >
                                About
                            </a>
                            <a 
                                href="#services" 
                                className="text-gray-700 hover:text-red-pink transition-colors px-2 py-1 font-medium"
                                onClick={(e) => {
                                    e.preventDefault();
                                    document.getElementById('services')?.scrollIntoView({ behavior: 'smooth' });
                                }}
                            >
                                Services
                            </a>
                            <a 
                                href="#testimonials" 
                                className="text-gray-700 hover:text-red-pink transition-colors px-2 py-1 font-medium"
                                onClick={(e) => {
                                    e.preventDefault();
                                    document.getElementById('testimonials')?.scrollIntoView({ behavior: 'smooth' });
                                }}
                            >
                                Testimonials
                            </a>
                            <a 
                                href="#faq" 
                                className="text-gray-700 hover:text-red-pink transition-colors px-2 py-1 font-medium"
                                onClick={(e) => {
                                    e.preventDefault();
                                    document.getElementById('faq')?.scrollIntoView({ behavior: 'smooth' });
                                }}
                            >
                                FAQ
                            </a>
                            <a 
                                href="#contact" 
                                className="text-gray-700 hover:text-red-pink transition-colors px-2 py-1 font-medium"
                                onClick={(e) => {
                                    e.preventDefault();
                                    document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
                                }}
                            >
                                Contact
                            </a>
                        </nav>
                        
                        <div className="hidden md:flex items-center space-x-4">
                            {auth.user ? (
                                <DropdownMenu>
                                    <DropdownMenuTrigger className="flex items-center space-x-2 focus:outline-none">
                                        <div className="w-10 h-10 rounded-full bg-red-pink flex items-center justify-center text-red-pink">
                                            <User className="w-5 h-5" />
                                        </div>
                                        <span className="font-medium text-gray-700">{auth.user.name}</span>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent className="w-56 mt-2" align="end">
                                        {route().has('dashboard') && (
                                            <Link href={route('dashboard')}>
                                                <DropdownMenuItem className="cursor-pointer">
                                                    <LayoutDashboard className="mr-2 h-4 w-4" />
                                                    <span>Dashboard</span>
                                                </DropdownMenuItem>
                                            </Link>
                                        )}
                                        {route().has('profile.edit') && (
                                            <Link href={route('profile.edit')}>
                                                <DropdownMenuItem className="cursor-pointer">
                                                    <Settings className="mr-2 h-4 w-4" />
                                                    <span>Profile</span>
                                                </DropdownMenuItem>
                                            </Link>
                                        )}
                                        <DropdownMenuItem asChild>
                                            <button 
                                                type="button"
                                                className="flex w-full items-center text-red-600 hover:text-red-700 px-2 py-1.5 text-sm"
                                                onClick={() => {
                                                    setIsMenuOpen(false);
                                                    router.post(route('logout'), {}, {
                                                        preserveScroll: true,
                                                        onSuccess: () => {
                                                            router.visit(route('login'));
                                                        },
                                                        onError: (errors) => {
                                                            console.error('Logout failed:', errors);
                                                        }
                                                    });
                                                }}
                                            >
                                                <LogOut className="mr-2 h-4 w-4" />
                                                <span>Logout</span>
                                            </button>
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            ) : (
                                <>
                                    <Link 
                                        href={route('login')} 
                                        className="px-4 py-2 border border-red-pink text-red-pink rounded-md hover:bg-red-pink/10 transition-colors"
                                    >
                                        Login
                                    </Link>
                                    <Link 
                                        href={route('register')}
                                        className="px-4 py-2 bg-red-pink text-white rounded-md hover:bg-red-pink/80 transition-colors"
                                    >
                                        Register
                                    </Link>
                                </>
                            )}
                        </div>

                        {/* Mobile menu button */}
                        <div className="md:hidden">
                            <button 
                                onClick={() => setIsMenuOpen(!isMenuOpen)}
                                className="p-2 -mr-2 rounded-md text-gray-700 hover:text-gray-900 focus:outline-none"
                                aria-label="Toggle menu"
                            >
                                <MenuIcon isOpen={isMenuOpen} />
                            </button>
                        </div>
                    </div>

                    {/* Mobile Navigation */}
                    <div className={`md:hidden ${isMenuOpen ? 'block' : 'hidden'}`}>
                        <div className="pt-4 pb-3 border-t border-gray-200">
                            <div className="flex flex-col space-y-3 px-2">
                                <a 
                                    href="#home" 
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-red-pink rounded-md"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        document.getElementById('home')?.scrollIntoView({ behavior: 'smooth' });
                                        setIsMenuOpen(false);
                                    }}
                                >
                                    Home
                                </a>
                                <a 
                                    href="#about" 
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-red-pink rounded-md"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' });
                                        setIsMenuOpen(false);
                                    }}
                                >
                                    About
                                </a>
                                <a 
                                    href="#services" 
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-red-pink rounded-md"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        document.getElementById('services')?.scrollIntoView({ behavior: 'smooth' });
                                        setIsMenuOpen(false);
                                    }}
                                >
                                    Services
                                </a>
                                <a 
                                    href="#testimonials" 
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-red-pink rounded-md"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        document.getElementById('testimonials')?.scrollIntoView({ behavior: 'smooth' });
                                        setIsMenuOpen(false);
                                    }}
                                >
                                    Testimonials
                                </a>
                                <a 
                                    href="#faq" 
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-red-pink rounded-md"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        document.getElementById('faq')?.scrollIntoView({ behavior: 'smooth' });
                                        setIsMenuOpen(false);
                                    }}
                                >
                                    FAQ
                                </a>
                                <a 
                                    href="#contact" 
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-red-pink rounded-md"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
                                        setIsMenuOpen(false);
                                    }}
                                >
                                    Contact
                                </a>
                                {auth.user ? (
                                    <div className="pt-2">
                                        <DropdownMenu open={isUserMenuOpen} onOpenChange={setIsUserMenuOpen}>
                                            <DropdownMenuTrigger asChild>
                                                <button className="w-full text-left px-3 py-2 rounded-md hover:bg-gray-50 transition-colors">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="w-10 h-10 rounded-full bg-red-pink flex items-center justify-center text-red-pink">
                                                            <User className="w-5 h-5" />
                                                        </div>
                                                        <div>
                                                            <p className="text-sm font-medium text-gray-900">{auth.user.name}</p>
                                                            <p className="text-xs text-gray-500">Tap to view options</p>
                                                        </div>
                                                    </div>
                                                </button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent className="w-full mx-2 mt-1" align="start">
                                                {route().has('dashboard') && (
                                                    <Link 
                                                        href={route('dashboard')}
                                                        onClick={() => {
                                                            setIsMenuOpen(false);
                                                            setIsUserMenuOpen(false);
                                                        }}
                                                    >
                                                        <DropdownMenuItem className="cursor-pointer">
                                                            <LayoutDashboard className="mr-2 h-4 w-4" />
                                                            <span>Dashboard</span>
                                                        </DropdownMenuItem>
                                                    </Link>
                                                )}
                                                
                                                {route().has('profile.edit') && (
                                                    <Link 
                                                        href={route('profile.edit')}
                                                        onClick={() => {
                                                            setIsMenuOpen(false);
                                                            setIsUserMenuOpen(false);
                                                        }}
                                                    >
                                                        <DropdownMenuItem className="cursor-pointer">
                                                            <Settings className="mr-2 h-4 w-4" />
                                                            <span>Profile</span>
                                                        </DropdownMenuItem>
                                                    </Link>
                                                )}
                                                
                                                <DropdownMenuItem asChild>
                                                    <button 
                                                        type="button"
                                                        className="flex w-full items-center text-red-600 hover:text-red-700 px-2 py-1.5 text-sm"
                                                        onClick={() => {
                                                            setIsMenuOpen(false);
                                                            setIsUserMenuOpen(false);
                                                            router.post(route('logout'), {}, {
                                                                preserveScroll: true,
                                                                onSuccess: () => {
                                                                    router.visit(route('login'));
                                                                },
                                                                onError: (errors) => {
                                                                    console.error('Logout failed:', errors);
                                                                }
                                                            });
                                                        }}
                                                    >
                                                        <LogOut className="mr-2 h-4 w-4" />
                                                        <span>Logout</span>
                                                    </button>
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                ) : (
                                    <div className="pt-2 space-y-2">
                                        <Link 
                                            href={route('login')}
                                            className="block w-full text-center px-4 py-2 border border-red-pink text-red-pink rounded-md hover:bg-red-pink-50 transition-colors"
                                            onClick={() => setIsMenuOpen(false)}
                                        >
                                            Login
                                        </Link>
                                        <Link 
                                            href={route('register')}
                                            className="block w-full text-center px-4 py-2 bg-red-pink text-white rounded-md hover:bg-red-pink transition-colors"
                                            onClick={() => setIsMenuOpen(false)}
                                        >
                                            Register
                                        </Link>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            {/* Add padding to account for fixed header */}
            <div className="h-20"></div>
            
            {/* Hero Section */}
            <main className="w-full px-4 sm:px-6 py-12 md:py-24 bg-[#f7f8fc]">
                <div className="max-w-7xl mx-auto">
                    <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
                        {/* Content - Left Side */}
                        <div className="w-full lg:w-1/2">
                            <div className="max-w-2xl mx-auto lg:mx-0">
                                <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                                    All Your <span className="text-red-pink font-extrabold">Bills</span> in One Place
                                </h1>
                                <p className="text-lg md:text-xl text-gray-600 mb-8">
                                    Pay bills, buy airtime, data, and more with just a few clicks. Fast, secure, and convenient bill payments at your fingertips.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4">
                                    <Link 
                                        href={route('register')}
                                        className="px-8 py-3 bg-red-pink text-white text-center rounded-md hover:bg-red-pink-700 transition-colors font-medium"
                                    >
                                        Get Started
                                    </Link>
                                    <Link 
                                        href="#features"
                                        className="px-8 py-3 border border-gray-300 text-gray-700 text-center rounded-md hover:bg-gray-50 transition-colors font-medium"
                                    >
                                        Learn More
                                    </Link>
                                </div>
                            </div>
                        </div>

                        {/* Image - Right Side */}
                        <div className="w-full lg:w-1/2 flex justify-center lg:justify-end">
                            <div className="relative w-full max-w-xs mx-auto">
                                <div className="absolute -top-4 -right-4 w-40 h-40 bg-red-pink-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
                                <div className="absolute -bottom-6 left-0 w-40 h-40 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
                                <div className="absolute -top-8 left-12 w-40 h-40 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
                                <div className="relative">
                                    <img 
                                        src="/images/app1.png" 
                                        alt="Bill payment illustration" 
                                        className="w-full h-auto object-contain"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            {/* About Us Section */}
            <section id="about" className="w-full bg-white px-4 sm:px-6 scroll-mt-20">
                <div className="max-w-7xl mx-auto py-12 md:py-20">
                    <div className="flex flex-col lg:flex-row items-center lg:items-start lg:gap-12">
                        {/* Left Side - Content */}
                        <div className="w-full lg:w-1/2 lg:pr-4 xl:pr-8">
                            <h2 className="text-2xl md:text-3xl font-black text-gray-900 mb-4 md:mb-6 leading-tight">
                                ABOUT US
                            </h2>
                            <p className="text-gray-600 text-base md:text-lg leading-relaxed space-y-4">
                                <span className="block">
                                    vita-minde is your all-in-one platform for convenient and reliable bill payments.
                                    Easily top up your airtime, internet data, cable TV, and electricity subscriptions, all in one place.
                                </span>
                                <span className="block">
                                    We offer all our services at the best and most affordable rates, helping you stay connected while saving money.
                                    Our platform is built with you in mind, delivering a fast, secure, and seamless experience that makes every transaction efficient and rewarding.
                                </span>
                            </p>
                        </div>
                        
                        {/* Right Side - Simple Image */}
                        <div className="w-full lg:w-1/2 mt-10 lg:mt-0">
                            <div className="relative mx-auto" style={{ width: '100%', maxWidth: '400px' }}>
                                <div className="relative w-full">
                                    <div className="w-full h-auto">
                                        <img 
                                            src="/images/shape2.png" 
                                            alt="vita-minde" 
                                            className="w-full h-auto"
                                            onError={(e) => {
                                                try {
                                                    const target = e.target as HTMLImageElement;
                                                    target.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0MDAgNDAwIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+CiAgPHRleHQgeD0iNTAwIiB5PSIyMDAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgZmlsbD0iIzAwMCI+U3VwcmVtZSBEYXJ0PC90ZXh0Pgo8L3N2Zz4=';
                                                } catch (error) {
                                                    console.error('Error loading fallback image:', error);
                                                }
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section id="features" className="w-full bg-gray-50 px-4 sm:px-6 py-16 md:py-24 scroll-mt-20">
                <div className="max-w-7xl mx-auto">
                    <h2 className="text-3xl md:text-4xl font-black text-center text-gray-900 mb-12 md:mb-16">
                        FEATURES
                    </h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
                        {[
                            'Airtime Purchase',
                            'Data Purchase',
                            'Cable TV Subscription',
                            'Electricity Bill',
                            'Exam Pin',
                            'Recharge Card',
                            'Data Card',
                            'Smile Topup',
                            'Alpha Topup',
                            'Referral System',
                            'Contact List',
                            'Finger Print Login',
                            'API Integration'
                        ].map((feature, index) => (
                            <div 
                                key={index}
                                className="bg-white rounded-lg border border-red-pink-200 p-4 md:p-6 flex items-center justify-center text-center hover:shadow-md transition-shadow duration-300"
                            >
                                <span className="text-gray-800 font-medium text-sm md:text-base">{feature}</span>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Why Choose Us Section */}
            <section id="why-choose-us" className="w-full bg-gray-50 px-4 sm:px-6 py-16 md:py-24 scroll-mt-20">
                <div className="max-w-7xl mx-auto">
                    <div className="text-center mb-12 md:mb-20">
                        <h2 className="text-2xl md:text-4xl font-black text-gray-900 mb-4">WHY CHOOSE US</h2>
                        <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
                            Why Should You Choose Our Bill Payment Platform
                        </p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {/* Card 1 - Customer Support */}
                        <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                            <div className="w-14 h-14 bg-pink-100 rounded-full flex items-center justify-center mb-4">
                                <img src="/images/icon_1.png" alt="Customer Support" className="w-8 h-8" />
                            </div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">Customer Support</h3>
                            <p className="text-gray-600">
                                We take customer support seriously. Our team of experts is always available, ready to listen to your suggestions and address any concerns or complaints promptly.
                            </p>
                        </div>

                        {/* Card 2 - User Friendly */}
                        <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                            <div className="w-14 h-14 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                                <img src="/images/icon_2.png" alt="User Friendly" className="w-8 h-8" />
                            </div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">User Friendly</h3>
                            <p className="text-gray-600">
                                Unlike other platforms, We keep things simple. Our platform is easy to use, flexible, and designed to help you get things done without confusion.
                            </p>
                        </div>

                        {/* Card 3 - Hybrid System */}
                        <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                            <div className="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                <img src="/images/icon-3.png" alt="Hybrid System" className="w-8 h-8" />
                            </div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">Hybrid System</h3>
                            <p className="text-gray-600">
                                All requests are automatically processed and delivered in real-time. Even if you run out of data but still have funds in your account, we'll handle your request via SMS, no internet needed.
                            </p>
                        </div>

                        {/* Card 4 - Secure */}
                        <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                            <div className="w-14 h-14 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                                <img src="/images/lock.png" alt="Secure" className="w-8 h-8" />
                            </div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">Secure</h3>
                            <p className="text-gray-600">
                                Your e-wallet is the safest, fastest, and most convenient way to make transactions. With PIN protection, your funds stay secure and can be stored for as long as you need them.
                            </p>
                        </div>

                        {/* Card 5 - Reliable */}
                        <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                            <div className="w-14 h-14 bg-cyan-100 rounded-full flex items-center justify-center mb-4">
                                <img src="/images/briefcase.png" alt="Reliable" className="w-8 h-8" />
                            </div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">Reliable</h3>
                            <p className="text-gray-600">
                                Our platform is built for maximum reliability and performance. By leveraging top-tier servers, we ensure minimal downtime and a seamless experience.
                            </p>
                        </div>

                        {/* Card 6 - Professional Team */}
                        <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                            <div className="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mb-4">
                                <img src="/images/coding.png" alt="Professional Team" className="w-8 h-8" />
                            </div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">Professional Team</h3>
                            <p className="text-gray-600">
                                Our team of seasoned experts understands the complexities of system architecture. We're equipped to tackle any challenge and deliver exceptional results.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Our Services Section */}
            <section id="services" className="w-full bg-white px-4 sm:px-6 py-16 md:py-24 scroll-mt-20">
                <div className="max-w-7xl mx-auto">
                    <h2 className="text-2xl md:text-4xl font-black text-center text-gray-900 mb-12">
                        OUR SERVICES
                    </h2>
                    <div className="flex flex-col lg:flex-row items-center gap-12">
                        {/* Left Side - Phone Images */}
                        <div className="w-full lg:w-1/2 relative">
                            <div className="relative">
                                <div className="relative w-full max-w-md mx-auto">
                                    <img 
                                        src="/images/app1.png" 
                                        alt="App Interface 1" 
                                        className="w-full max-w-xs mx-auto relative z-0 animate-[slideRightLeft_3s_ease-in-out_infinite]"
                                    />
                                    <img 
                                        src="/images/app3.png" 
                                        alt="App Interface 2" 
                                        className="w-full max-w-xs absolute top-6 -right-4 lg:top-8 lg:-right-12 z-10 animate-[slideLeftRight_3s_ease-in-out_infinite] animation-delay-500"
                                    />
                                </div>
                            </div>
                        </div>
                        
                        {/* Right Side - Services */}
                        <div className="w-full lg:w-1/2 space-y-8">
                            <div>
                                <h3 className="text-red-pink text-lg font-medium mb-2">What we do Best</h3>
                                <h2 className="text-3xl md:text-4xl font-black text-gray-900 mb-6">Bills Payment With Ease</h2>
                                
                                <ul className="space-y-4">
                                    {[
                                        'Buy Airtime At A Discount Rate',
                                        'Purchase Internet Data Subscription',
                                        'Renew Your Cable TV Subscription',
                                        'Pay For Your Electricity Subscription'
                                    ].map((service, index) => (
                                        <li key={index} className="flex items-start">
                                            <svg className="w-5 h-5 text-red-pink-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-gray-700">{service}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            
                            <div>
                                <h3 className="text-gray-900 text-xl font-medium mb-4">Other Value Added Services</h3>
                                <ul className="space-y-4">
                                    {[
                                        'Web Design And Development',
                                        'Mobile App Development',
                                        'Graphic Design',
                                        'Digital Marketing',
                                        'Technical Consultation'
                                    ].map((service, index) => (
                                        <li key={index} className="flex items-start">
                                            <svg className="w-5 h-5 text-red-pink-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-gray-700">{service}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Partnership Section */}
            <section id="partnership" className="w-full bg-red-pink py-16 md:py-20 px-4 sm:px-6 scroll-mt-20">
                <div className="max-w-7xl mx-auto">
                    <div className="flex flex-col lg:flex-row items-center justify-between gap-8">
                        <div className="lg:max-w-2xl">
                            <h2 className="text-3xl md:text-4xl font-black text-white mb-4">Partnership</h2>
                            <p className="text-white text-lg md:text-xl">
                                Do you want to own your own VTU and Data Purchase Platform? Lets help you get started. 
                                You could be making a minimum of ₦100,000 a month.
                            </p>
                        </div>
                        <button className="bg-gray-900 hover:bg-gray-800 text-white font-medium py-3 px-8 rounded-md transition-colors whitespace-nowrap">
                            Get Started Now
                        </button>
                    </div>
                </div>
            </section>

            {/* Testimonial Section */}
            <section id="testimonials" className="w-full bg-white relative overflow-hidden pt-20 pb-24 scroll-mt-20">
                <div className="max-w-6xl mx-auto px-4 sm:px-6 relative z-10">
                    <div className="relative">
                        {/* Left Navigation Button */}
                        <button 
                            onClick={prevTestimonial}
                            className="absolute left-0 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white shadow-md flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors z-10"
                            aria-label="Previous testimonial"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>

                        {/* Testimonial Content */}
                        <div className="relative overflow-hidden h-96">
                            {testimonials.map((testimonial, index) => (
                                <div 
                                    key={testimonial.id}
                                    ref={index === 0 ? testimonialRef : null}
                                    className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${index === currentTestimonial ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                                    style={{
                                        transform: `translateX(${(index - currentTestimonial) * 100}%)`,
                                        transition: 'opacity 0.8s ease-in-out, transform 0.8s ease-in-out',
                                    }}
                                >
                                    <div className="text-center max-w-4xl mx-auto">
                                        <div className="w-24 h-24 rounded-full overflow-hidden mx-auto mb-6 border-4 border-white shadow-lg">
                                            <img 
                                                src={testimonial.image} 
                                                alt={testimonial.name} 
                                                className="w-full h-full object-cover"
                                            />
                                        </div>
                                        <h3 className="text-xl font-bold text-gray-900">{testimonial.name}</h3>
                                        <p className="text-gray-600 mb-8">{testimonial.role}</p>
                                        
                                        <blockquote className="max-w-3xl mx-auto">
                                            <p className="text-2xl md:text-3xl font-black text-gray-900 mb-6">"{testimonial.quote}"</p>
                                            <p className="text-lg text-gray-600">
                                                {testimonial.content}
                                            </p>
                                        </blockquote>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Right Navigation Button */}
                        <button 
                            onClick={nextTestimonial}
                            className="absolute right-0 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white shadow-md flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors z-10"
                            aria-label="Next testimonial"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </section>

            {/* Statistics Section */}
            <section id="statistics" className="w-full bg-gray-50 py-16 md:py-20 scroll-mt-20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 max-w-4xl mx-auto">
                        {/* Happy Clients */}
                        <div className="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
                            <div className="text-4xl md:text-5xl font-black text-red-pink-600 mb-2">
                                <AnimatedNumber value={10000} duration={2000} />+
                            </div>
                            <p className="text-gray-600 text-lg font-medium">Happy Clients</p>
                        </div>
                        
                        {/* Projects Completed */}
                        <div className="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
                            <div className="text-4xl md:text-5xl font-black text-red-pink-600 mb-2">
                                <AnimatedNumber value={200} duration={1500} />+
                            </div>
                            <p className="text-gray-600 text-lg font-medium">Projects Completed</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* FAQ Section */}
            <section id="faq" className="w-full bg-white py-16 md:py-24 px-4 sm:px-6 scroll-mt-20">
                <div className="max-w-7xl mx-auto">
                    <div className="flex flex-col lg:flex-row gap-12">
                        {/* Left Side - Title and Description */}
                        <div className="lg:w-1/3">
                            <h2 className="text-3xl md:text-4xl font-black text-gray-900 mb-4">Frequently Asked questions.</h2>
                            <p className="text-gray-600">
                            These are some frequently asked questions to give you more information about our platform and help you get started. If you have any other question, please do contact our customer support team.
                            </p>
                        </div>
                        
                        {/* Right Side - FAQ Accordion */}
                        <div className="lg:w-2/3 space-y-4">
                            {[
                                {
                                    question: "1. How Do I Buy Airtime?",
                                    answer: (
                                        <ol className="list-decimal pl-5 space-y-2 text-gray-600">
                                            <li>First create an account and login.</li>
                                            <li>After login click fund wallet from the dashboard menu.</li>
                                            <li>Fund your wallet using your preferred option.</li>
                                            <li>After funding your wallet, click on Airtime on the menu bar.</li>
                                            <li>Select network, enter phone number, and provide a transaction pin to complete the transaction.</li>
                                        </ol>
                                    )
                                },
                                {
                                    question: "2. How Do I Buy Data?",
                                    answer: "Follow the same steps as buying airtime, but select \"Data\" from the menu instead of \"Airtime\" and choose your preferred data plan."
                                },
                                {
                                    question: "3. How Do I Check My Data Balance?",
                                    answer: "You can check your data balance by dialing the appropriate USSD code for your network provider or by checking in your device settings under mobile data usage."
                                },
                                {
                                    question: "4. Is the data plan compatible for all device/modem/WiFi or MiFi?",
                                    answer: "Yes, our data plans are compatible with all devices including smartphones, tablets, modems, WiFi routers, and MiFi devices. Just make sure to select the correct plan for your device type when purchasing."
                                }
                            ].map((faq, index) => (
                                <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                                    <button 
                                        onClick={() => toggleFaq(index)}
                                        className="w-full flex justify-between items-center p-4 md:p-6 text-left hover:bg-gray-50 transition-colors"
                                        aria-expanded={openFaqIndex === index}
                                        aria-controls={`faq${index}-content`}
                                    >
                                        <span className="text-lg font-medium text-gray-900">{faq.question}</span>
                                        <svg 
                                            className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${openFaqIndex === index ? 'rotate-180' : ''}`} 
                                            fill="none" 
                                            stroke="currentColor" 
                                            viewBox="0 0 24 24" 
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div 
                                        id={`faq${index}-content`} 
                                        className={`${openFaqIndex === index ? 'block' : 'hidden'} p-4 md:p-6 pt-0`} 
                                        aria-hidden={openFaqIndex !== index}
                                    >
                                        {typeof faq.answer === 'string' ? (
                                            <p className="text-gray-600">{faq.answer}</p>
                                        ) : faq.answer}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>


            {/* CTA Section */}
            <section id="cta" className="py-20 bg-red-pink text-white scroll-mt-20">
                <div className="container mx-auto px-6 text-center">
                    <h2 className="text-4xl md:text-5xl font-black mb-4">Get Started</h2>
                    <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                        Start Enjoying All Of Our Services At The Best Affordable Price
                    </p>
                    <div className="flex flex-wrap justify-center gap-4 mb-8">
                        <Link 
                            href={route('register')}
                            className="px-8 py-4 bg-gray-900 text-white font-medium rounded-md hover:bg-gray-800 transition-colors"
                        >
                            Register Now
                        </Link>
                        <Link 
                            href="#contact"
                            className="px-8 py-4 bg-gray-900 text-white font-medium rounded-md hover:bg-gray-800 transition-colors"
                        >
                            Contact Us
                        </Link>
                    </div>
                    <div className="flex flex-wrap justify-center gap-6 mt-6">
                        {['Trusted', 'Fast', 'Secure'].map((item) => (
                            <div key={item} className="flex items-center">
                                <svg className="w-6 h-6 text-red-pink-300 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span className="text-red-pink-100 font-medium">{item}</span>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Footer */}
            <footer id="contact" className="bg-gray-100 text-gray-700 py-12 scroll-mt-20">
                <div className="container mx-auto px-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {/* Logo and Description */}
                        <div className="space-y-4">
                            <div className="flex items-center space-x-2">
                               
                                {/* <img src="/images/logo.png" className='h-auto w-8 sm:w-10' alt="vita-minde Logo" /> */}
                                <img src="/images/logo.png" className='h-auto w-36 md:w-32 lg:w-40 xl:w-48 sm:w-36' alt="vita-minde Logo" />
                                {/* <span className="text-3xl font-bold text-gray-900">vita-minde</span> */}
                            </div>
                            <p className="text-gray-600">
                                Your one-stop platform for all bills payment. Trusted, Fast, and Secure
                            </p>
                        </div>

                        {/* About */}
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">About</h3>
                            <ul className="space-y-2">
                                <li><a href="#about" className="hover:text-red-pink-600 transition-colors">About Us</a></li>
                                <li><a href="#services" className="hover:text-red-pink-600 transition-colors">Services</a></li>
                            </ul>
                        </div>

                        {/* Partnership */}
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Partnership</h3>
                            <ul className="space-y-2">
                                <li><a href="#partnership" className="hover:text-red-pink-600 transition-colors">Portal Owner</a></li>
                                <li><a href="#" className="hover:text-red-pink-600 transition-colors">Api Documentation</a></li>
                            </ul>
                        </div>

                        {/* Contact */}
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact</h3>
                            <ul className="space-y-3">
                                <li className="flex items-start">
                                    <svg className="w-5 h-5 text-red-pink-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <span>6th Avenue, Loloama, Port Harcourt, River State.</span>
                                </li>
                                <li className="flex items-center">
                                    <svg className="w-5 h-5 text-red-pink-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    <a href="tel:08133518086" className="hover:text-red-pink-600 transition-colors">09033227650</a>
                                </li>
                                <li className="flex items-center">
                                    <svg className="w-5 h-5 text-red-pink-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    <a href="mailto:<EMAIL>" className="hover:text-red-pink-600 transition-colors"><EMAIL></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div className="border-t border-gray-200 mt-12 pt-8 text-center">
                        <p className="text-gray-600">© 2025 vita-minde - All Rights Reserved</p>
                    </div>
                </div>
            </footer>
        </div>
    );
}

// Animated Number Component
interface AnimatedNumberProps {
    value: number;
    duration?: number;
}

const AnimatedNumber = ({ value, duration = 2000 }: AnimatedNumberProps) => {
    const [count, setCount] = useState(0);
    const [isVisible, setIsVisible] = useState(false);
    const ref = useRef(null);

    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    observer.disconnect();
                }
            },
            { threshold: 0.5 }
        );

        if (ref.current) {
            observer.observe(ref.current);
        }

        return () => observer.disconnect();
    }, []);

    useEffect(() => {
        if (!isVisible) return;
        
        const startTime = Date.now();
        const endValue = value;
        const startValue = Math.floor(endValue * 0.1); // Start from 10% of the target value
        const range = endValue - startValue;
        
        const animate = () => {
            const now = Date.now();
            const progress = Math.min((now - startTime) / duration, 1);
            
            // Ease out function for smooth deceleration
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const current = Math.floor(startValue + (range * easeOut));
            
            setCount(current);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                setCount(endValue); // Ensure we end exactly at the target value
            }
        };
        
        animate();
    }, [isVisible, value, duration]);

    return <span ref={ref}>{count.toLocaleString()}</span>;
};

// Add smooth scrolling behavior
const scrollStyles = `
    html {
        scroll-behavior: smooth;
        scroll-padding-top: 80px; /* Account for fixed header */
    }
    @media (prefers-reduced-motion: reduce) {
        html {
            scroll-behavior: auto;
        }
    }
`;

// Add some custom animations
const styles = `
    ${scrollStyles}
    .stat-item {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }
  
  .stat-item.visible {
    opacity: 1;
    transform: translateY(0);
  }

@keyframes slideRightLeft {
        0%, 100% { transform: translateX(0); }
        50% { transform: translateX(10px); }
    }
    
    @keyframes slideLeftRight {
        0%, 100% { transform: translateX(0); }
        50% { transform: translateX(-10px); }
    }

    @keyframes blob {
        0% { transform: translate(0px, 0px) scale(1); }
        33% { transform: translate(30px, -50px) scale(1.1); }
        66% { transform: translate(-20px, 20px) scale(0.9); }
        100% { transform: translate(0px, 0px) scale(1); }
    }
    .animate-blob {
        animation: blob 7s infinite;
    }
    .animation-delay-2000 {
        animation-delay: 2s;
    }
    .animation-delay-4000 {
        animation-delay: 4s;
    }
`;

// Add styles to the head
if (typeof document !== 'undefined') {
    const styleElement = document.createElement('style');
    styleElement.innerHTML = styles;
    document.head.appendChild(styleElement);
}